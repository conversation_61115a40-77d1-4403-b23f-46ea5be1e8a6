{"__meta": {"id": "X51547a35e266f6cb9841f402333013c6", "datetime": "2025-07-26 11:15:12", "utime": 1753539312.964897, "method": "PUT", "uri": "/api/v1/dashboard/admin/orders/1016", "ip": "127.0.0.1"}, "php": {"version": "8.2.26", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[11:15:12] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753539312.657962, "xdebug_link": null, "collector": "log"}, {"message": "[11:15:12] LOG.warning: Implicit conversion from float 144.57999999999998 to int loses precision in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasRelationships.php on line 786", "message_html": null, "is_string": false, "label": "warning", "time": 1753539312.959535, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753539312.427096, "end": 1753539312.964913, "duration": 0.5378170013427734, "duration_str": "538ms", "measures": [{"label": "Booting", "start": 1753539312.427096, "relative_start": 0, "end": 1753539312.642829, "relative_end": 1753539312.642829, "duration": 0.21573305130004883, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753539312.642838, "relative_start": 0.2157421112060547, "end": 1753539312.964915, "relative_end": 2.1457672119140625e-06, "duration": 0.32207703590393066, "duration_str": "322ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 46357656, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT api/v1/dashboard/admin/orders/{order}", "middleware": "api, block.ip, sanctum.check, role:admin|manager", "as": "admin.orders.update", "controller": "App\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController@update", "namespace": null, "prefix": "api/v1/dashboard/admin", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\OSPanel\\home\\api.ticketflow.chat\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php&line=214\">\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php:214-232</a>"}, "queries": {"nb_statements": 96, "nb_failed_statements": 0, "accumulated_duration": 0.05789999999999998, "accumulated_duration_str": "57.9ms", "statements": [{"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 51}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.01831, "duration_str": "18.31ms", "stmt_id": "\\app\\Repositories\\CoreRepository.php:51", "connection": "foodyman", "start_percent": 0, "width_percent": 31.623}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 25}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:41", "connection": "foodyman", "start_percent": 31.623, "width_percent": 0.501}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 51}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:51", "connection": "foodyman", "start_percent": 32.124, "width_percent": 0.535}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 25}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:41", "connection": "foodyman", "start_percent": 32.66, "width_percent": 0.484}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 33.143, "width_percent": 0.777}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 33.921, "width_percent": 0.518}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '17' limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 64}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php:64", "connection": "foodyman", "start_percent": 34.439, "width_percent": 0.95}, {"sql": "select * from `users` where `users`.`id` = 101 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["101"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 137}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:137", "connection": "foodyman", "start_percent": 35.389, "width_percent": 0.691}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-07-26 11:15:12', `personal_access_tokens`.`updated_at` = '2025-07-26 11:15:12' where `id` = 17", "type": "query", "params": [], "bindings": ["2025-07-26 11:15:12", "2025-07-26 11:15:12", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 19, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 21, "namespace": "middleware", "name": "block.ip", "line": 24}], "duration": 0.0014399999999999999, "duration_str": "1.44ms", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:83", "connection": "foodyman", "start_percent": 36.079, "width_percent": 2.487}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (101) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 235}, {"index": 23, "namespace": "middleware", "name": "role", "line": 29}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 25, "namespace": "middleware", "name": "sanctum.check", "line": 18}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:188", "connection": "foodyman", "start_percent": 38.566, "width_percent": 0.933}, {"sql": "select count(*) as aggregate from `users` where `id` = 109 and `deleted_at` is null", "type": "query", "params": [], "bindings": ["109"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 39.499, "width_percent": 0.846}, {"sql": "select count(*) as aggregate from `currencies` where `id` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 40.345, "width_percent": 0.484}, {"sql": "select count(*) as aggregate from `shops` where `id` = 501 and `deleted_at` is null", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 40.829, "width_percent": 0.622}, {"sql": "select count(*) as aggregate from `stocks` where `id` = 3 and `deleted_at` is null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 41.451, "width_percent": 0.484}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 10, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 721}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 128}, {"index": 17, "namespace": "middleware", "name": "role", "line": 30}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 19, "namespace": "middleware", "name": "sanctum.check", "line": 18}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 21, "namespace": "middleware", "name": "block.ip", "line": 24}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Middleware\\TrustLicence.php", "line": 65}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 27, "namespace": "middleware", "name": "throttle", "line": 127}, {"index": 28, "namespace": "middleware", "name": "throttle", "line": 58}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 103}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 719}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 698}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 662}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 651}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 167}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 128}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31}, {"index": 41, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 42, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}, {"index": 43, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 40}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 45, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php", "line": 27}, {"index": 46, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 47, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 86}, {"index": 48, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 49, "namespace": null, "name": "\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php", "line": 52}], "duration": 0, "duration_str": "", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:226", "connection": "foodyman", "start_percent": 41.934, "width_percent": 0}, {"sql": "select * from `shops` where `shops`.`id` = 501 and `shops`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 229}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:229", "connection": "foodyman", "start_percent": 41.934, "width_percent": 0.674}, {"sql": "select * from `users` where `users`.`id` in (103) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 229}, {"index": 25, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:229", "connection": "foodyman", "start_percent": 42.608, "width_percent": 0.57}, {"sql": "select * from `orders` where `orders`.`id` = 1016 limit 1", "type": "query", "params": [], "bindings": ["1016"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 232}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:232", "connection": "foodyman", "start_percent": 43.178, "width_percent": 0.656}, {"sql": "select * from `order_details` where `order_details`.`order_id` in (1016)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 232}, {"index": 25, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:232", "connection": "foodyman", "start_percent": 43.834, "width_percent": 0.829}, {"sql": "select `id` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 752}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 241}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:752", "connection": "foodyman", "start_percent": 44.663, "width_percent": 0.553}, {"sql": "select * from `currencies` where `currencies`.`id` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 755}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 241}, {"index": 24, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:755", "connection": "foodyman", "start_percent": 45.216, "width_percent": 0.501}, {"sql": "select * from `settings` where `key` = 'service_fee' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["service_fee"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 772}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 241}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:772", "connection": "foodyman", "start_percent": 45.717, "width_percent": 0.484}, {"sql": "update `orders` set `total_price` = 0, `note` = '', `commission_fee` = 0, `status` = 'new', `phone` = '', `delivery_date` = '2025-07-28', `change_required` = 0, `change_amount` = '', `otp` = 9867, `orders`.`updated_at` = '2025-07-26 11:15:12' where `id` = 1016", "type": "query", "params": [], "bindings": ["0", "", "0", "new", "", "2025-07-28", "0", "", "9867", "2025-07-26 11:15:12", "1016"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 241}, {"index": 18, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:241", "connection": "foodyman", "start_percent": 46.2, "width_percent": 1.261}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\OrderObserver.php", "line": 48}, {"index": 24, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 241}, {"index": 28, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 47.461, "width_percent": 0.484}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\OrderObserver.php", "line": 48}, {"index": 24, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 241}, {"index": 28, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 47.945, "width_percent": 0.587}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'model_logs'", "type": "query", "params": [], "bindings": ["foodyman", "model_logs"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\ModelLogService\\ModelLogService.php", "line": 30}, {"index": 21, "namespace": null, "name": "\\app\\Observers\\OrderObserver.php", "line": 48}, {"index": 28, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 241}, {"index": 32, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Services\\ModelLogService\\ModelLogService.php:30", "connection": "foodyman", "start_percent": 48.532, "width_percent": 1.364}, {"sql": "insert into `model_logs` (`model_type`, `model_id`, `data`, `type`, `created_at`, `created_by`) values ('App\\Models\\Order', 1016, '{\\\"total_price\\\":69.78,\\\"note\\\":\\\" |  |  | \\\",\\\"tax\\\":0,\\\"commission_fee\\\":3.489,\\\"status\\\":\\\"on_a_way\\\",\\\"phone\\\":\\\"9989119121242\\\",\\\"delivery_date\\\":\\\"2025-07-25\\\",\\\"change_required\\\":1,\\\"change_amount\\\":\\\"100.00\\\",\\\"waiter_fee\\\":0,\\\"service_fee\\\":0,\\\"address\\\":\\\"{\\\\\"address\\\\\":\\\\\"Rua 3, 34 - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\\\\u00f3polis - GO, 75860-000, Brasil\\\\\",\\\\\"office\\\\\":null,\\\\\"house\\\\\":null,\\\\\"floor\\\\\":null}\\\",\\\"location\\\":\\\"{\\\\\"latitude\\\\\":\\\\\"-18.439344666666663\\\\\",\\\\\"longitude\\\\\":\\\\\"-50.43331116666666\\\\\"}\\\",\\\"otp\\\":6604}', 'order_updated', '2025-07-26 11:15:12', 101)", "type": "query", "params": [], "bindings": ["App\\Models\\Order", "1016", "{&quot;total_price&quot;:69.78,&quot;note&quot;:&quot; |  |  | &quot;,&quot;tax&quot;:0,&quot;commission_fee&quot;:3.489,&quot;status&quot;:&quot;on_a_way&quot;,&quot;phone&quot;:&quot;9989119121242&quot;,&quot;delivery_date&quot;:&quot;2025-07-25&quot;,&quot;change_required&quot;:1,&quot;change_amount&quot;:&quot;100.00&quot;,&quot;waiter_fee&quot;:0,&quot;service_fee&quot;:0,&quot;address&quot;:&quot;{\\&quot;address\\&quot;:\\&quot;Rua 3, 34 - <PERSON><PERSON><PERSON>, <PERSON><PERSON>\\\\u00f3polis - GO, 75860-000, Brasil\\&quot;,\\&quot;office\\&quot;:null,\\&quot;house\\&quot;:null,\\&quot;floor\\&quot;:null}&quot;,&quot;location&quot;:&quot;{\\&quot;latitude\\&quot;:\\&quot;-18.439344666666663\\&quot;,\\&quot;longitude\\&quot;:\\&quot;-50.43331116666666\\&quot;}&quot;,&quot;otp&quot;:6604}", "order_updated", "2025-07-26 11:15:12", "101"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\ModelLogService\\ModelLogService.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\app\\Observers\\OrderObserver.php", "line": 48}, {"index": 29, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 241}, {"index": 33, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 34, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Services\\ModelLogService\\ModelLogService.php:30", "connection": "foodyman", "start_percent": 49.896, "width_percent": 0.639}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 50.535, "width_percent": 0.501}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 51.036, "width_percent": 0.484}, {"sql": "select * from `stocks` where `stocks`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 40}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 25, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:40", "connection": "foodyman", "start_percent": 51.52, "width_percent": 0.518}, {"sql": "update `stocks` set `quantity` = `quantity` + 1 where `id` = 3 and `stocks`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 40}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:40", "connection": "foodyman", "start_percent": 52.038, "width_percent": 1.554}, {"sql": "delete from `cart_details` where `cart_details`.`stock_id` = 3 and `cart_details`.`stock_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Observers\\StockObserver.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 40}, {"index": 24, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 28, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 29, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\app\\Observers\\StockObserver.php:36", "connection": "foodyman", "start_percent": 53.592, "width_percent": 1.002}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\StockObserver.php", "line": 38}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 40}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 54.594, "width_percent": 0.57}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\StockObserver.php", "line": 38}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 40}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}], "duration": 0.00025, "duration_str": "250μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 55.164, "width_percent": 0.432}, {"sql": "insert into `model_logs` (`model_type`, `model_id`, `data`, `type`, `created_at`, `created_by`) values ('App\\Models\\Stock', 3, '{\\\"quantity\\\":2}', 'stock_updated', '2025-07-26 11:15:12', 101)", "type": "query", "params": [], "bindings": ["App\\Models\\Stock", "3", "{&quot;quantity&quot;:2}", "stock_updated", "2025-07-26 11:15:12", "101"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\ModelLogService\\ModelLogService.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\app\\Observers\\StockObserver.php", "line": 38}, {"index": 31, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 40}, {"index": 32, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 36, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Services\\ModelLogService\\ModelLogService.php:30", "connection": "foodyman", "start_percent": 55.596, "width_percent": 0.501}, {"sql": "delete from `order_details` where `id` = 517", "type": "query", "params": [], "bindings": ["517"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 42}, {"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 19, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:42", "connection": "foodyman", "start_percent": 56.097, "width_percent": 0.535}, {"sql": "select * from `stocks` where `stocks`.`id` = 3 and `stocks`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 62}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 47}, {"index": 18, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:62", "connection": "foodyman", "start_percent": 56.632, "width_percent": 0.518}, {"sql": "select `id`, `status`, `shop_id`, `active`, `min_qty`, `max_qty`, `tax`, `img`, `interval` from `products` where `products`.`id` in (2) and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 62}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 47}, {"index": 23, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:62", "connection": "foodyman", "start_percent": 57.15, "width_percent": 0.656}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` in (2) and `start` <= '2025-07-26 00:00:00' and `end` >= '2025-07-26 00:00:00' and `active` = 1 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-07-26 00:00:00", "2025-07-26 00:00:00", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 62}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 47}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 31, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:62", "connection": "foodyman", "start_percent": 57.807, "width_percent": 0.725}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'order_details'", "type": "query", "params": [], "bindings": ["foodyman", "order_details"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 80}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 47}, {"index": 18, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:80", "connection": "foodyman", "start_percent": 58.532, "width_percent": 1.589}, {"sql": "insert into `order_details` (`note`, `origin_price`, `tax`, `discount`, `total_price`, `stock_id`, `parent_id`, `quantity`, `bonus`, `kitchen_id`, `order_id`, `updated_at`, `created_at`) values ('', 65, 0, 0, 65, 3, '', 1, 0, '', 1016, '2025-07-26 11:15:12', '2025-07-26 11:15:12')", "type": "query", "params": [], "bindings": ["", "65", "0", "0", "65", "3", "", "1", "0", "", "1016", "2025-07-26 11:15:12", "2025-07-26 11:15:12"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 80}, {"index": 19, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 47}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 24, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:80", "connection": "foodyman", "start_percent": 60.121, "width_percent": 0.622}, {"sql": "update `stocks` set `quantity` = `quantity` - 1 where `id` = 3 and `stocks`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 82}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 47}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:82", "connection": "foodyman", "start_percent": 60.743, "width_percent": 1.019}, {"sql": "delete from `cart_details` where `cart_details`.`stock_id` = 3 and `cart_details`.`stock_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Observers\\StockObserver.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 82}, {"index": 24, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 47}, {"index": 25, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 29, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Observers\\StockObserver.php:36", "connection": "foodyman", "start_percent": 61.762, "width_percent": 0.518}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\StockObserver.php", "line": 38}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 82}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 47}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 62.28, "width_percent": 0.466}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\StockObserver.php", "line": 38}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 82}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 47}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 62.746, "width_percent": 0.518}, {"sql": "insert into `model_logs` (`model_type`, `model_id`, `data`, `type`, `created_at`, `created_by`) values ('App\\Models\\Stock', 3, '{\\\"quantity\\\":3}', 'stock_updated', '2025-07-26 11:15:12', 101)", "type": "query", "params": [], "bindings": ["App\\Models\\Stock", "3", "{&quot;quantity&quot;:3}", "stock_updated", "2025-07-26 11:15:12", "101"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\ModelLogService\\ModelLogService.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\app\\Observers\\StockObserver.php", "line": 38}, {"index": 31, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 82}, {"index": 32, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 47}, {"index": 33, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Services\\ModelLogService\\ModelLogService.php:30", "connection": "foodyman", "start_percent": 63.264, "width_percent": 0.466}, {"sql": "select * from `products` where `products`.`id` = 2 and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Helpers\\Admin\\Utility.php", "line": 24}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 117}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 47}, {"index": 23, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Helpers\\Admin\\Utility.php:24", "connection": "foodyman", "start_percent": 63.731, "width_percent": 0.57}, {"sql": "select * from `shops` where `shops`.`id` = 501 and `shops`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Helpers\\Admin\\Utility.php", "line": 24}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 117}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 47}, {"index": 23, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "\\app\\Helpers\\Admin\\Utility.php:24", "connection": "foodyman", "start_percent": 64.301, "width_percent": 0.604}, {"sql": "select * from `users` where `users`.`id` = 103 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["103"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Helpers\\Admin\\Utility.php", "line": 24}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 117}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 47}, {"index": 23, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Helpers\\Admin\\Utility.php:24", "connection": "foodyman", "start_percent": 64.905, "width_percent": 0.674}, {"sql": "select `value` from `translations` where `key` = 'inventory' and `translations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["inventory"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Helpers\\Admin\\Utility.php", "line": 25}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 117}, {"index": 18, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 47}, {"index": 19, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 23, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}], "duration": 0.00294, "duration_str": "2.94ms", "stmt_id": "\\app\\Helpers\\Admin\\Utility.php:25", "connection": "foodyman", "start_percent": 65.579, "width_percent": 5.078}, {"sql": "select * from `stock_inventory_items` where `stock_inventory_items`.`stock_id` = 3 and `stock_inventory_items`.`stock_id` is not null and `stock_inventory_items`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Helpers\\Admin\\Utility.php", "line": 27}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 117}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 47}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\app\\Helpers\\Admin\\Utility.php:27", "connection": "foodyman", "start_percent": 70.656, "width_percent": 0.95}, {"sql": "select * from `product_inventory_items` where `product_inventory_items`.`product_id` = 2 and `product_inventory_items`.`product_id` is not null and `product_inventory_items`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Helpers\\Admin\\Utility.php", "line": 28}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 117}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 47}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 251}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Helpers\\Admin\\Utility.php:28", "connection": "foodyman", "start_percent": 71.606, "width_percent": 0.777}, {"sql": "select * from `orders` where `id` = 1016 limit 1", "type": "query", "params": [], "bindings": ["1016"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 331}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:331", "connection": "foodyman", "start_percent": 72.383, "width_percent": 0.639}, {"sql": "select * from `order_details` where `order_details`.`order_id` in (1016)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 331}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:331", "connection": "foodyman", "start_percent": 73.022, "width_percent": 0.501}, {"sql": "select * from `transactions` where `type` = 'model' and `parent_id` is null and `transactions`.`payable_id` in (1016) and `transactions`.`payable_type` = 'App\\Models\\Order' and `transactions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["model", "App\\Models\\Order"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 331}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:331", "connection": "foodyman", "start_percent": 73.523, "width_percent": 0.76}, {"sql": "select * from `users` where `users`.`id` in (109)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 331}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:331", "connection": "foodyman", "start_percent": 74.283, "width_percent": 0.501}, {"sql": "select * from `order_coupons` where `order_coupons`.`order_id` in (1016)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 331}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:331", "connection": "foodyman", "start_percent": 74.784, "width_percent": 0.501}, {"sql": "select * from `shops` where `shops`.`id` in (501)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 331}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:331", "connection": "foodyman", "start_percent": 75.285, "width_percent": 0.656}, {"sql": "select * from `shop_subscriptions` where date(`expired_at`) >= '2025-07-26' and (`active` = 1) and `shop_subscriptions`.`shop_id` in (501) and `shop_subscriptions`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": ["2025-07-26", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 331}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}, {"index": 31, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:331", "connection": "foodyman", "start_percent": 75.941, "width_percent": 0.708}, {"sql": "select * from `stocks` where `stocks`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 920}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 340}, {"index": 23, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:920", "connection": "foodyman", "start_percent": 76.649, "width_percent": 0.553}, {"sql": "select * from `receipts` where exists (select * from `stocks` inner join `receipt_stocks` on `stocks`.`id` = `receipt_stocks`.`stock_id` where `receipts`.`id` = `receipt_stocks`.`receipt_id` and `stocks`.`deleted_at` is null) and `shop_id` = 501 and `receipts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 935}, {"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 340}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:935", "connection": "foodyman", "start_percent": 77.202, "width_percent": 1.002}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 937}, {"index": 18, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 340}, {"index": 19, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 78.204, "width_percent": 0.898}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 937}, {"index": 18, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 340}, {"index": 19, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 79.102, "width_percent": 0.708}, {"sql": "select * from `settings` where `key` = 'by_subscription' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["by_subscription"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 342}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:342", "connection": "foodyman", "start_percent": 79.81, "width_percent": 0.501}, {"sql": "select * from `settings` where `key` = 'service_fee' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["service_fee"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 343}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00023999999999999998, "duration_str": "240μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:343", "connection": "foodyman", "start_percent": 80.311, "width_percent": 0.415}, {"sql": "select * from `coupons` where `name` is null and `shop_id` = 501 and `qty` > 0 and date(`expired_at`) > '2025-07-26' and `coupons`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["501", "0", "2025-07-26"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 347}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:347", "connection": "foodyman", "start_percent": 80.725, "width_percent": 0.725}, {"sql": "update `orders` set `total_price` = 69.78, `commission_fee` = 3.489, `orders`.`updated_at` = '2025-07-26 11:15:12' where `id` = 1016", "type": "query", "params": [], "bindings": ["69.78", "3.489", "2025-07-26 11:15:12", "1016"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 419}, {"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}, {"index": 19, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:419", "connection": "foodyman", "start_percent": 81.451, "width_percent": 1.399}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\OrderObserver.php", "line": 48}, {"index": 24, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 419}, {"index": 25, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 82.85, "width_percent": 0.518}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\OrderObserver.php", "line": 48}, {"index": 24, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 419}, {"index": 25, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 83.368, "width_percent": 0.466}, {"sql": "insert into `model_logs` (`model_type`, `model_id`, `data`, `type`, `created_at`, `created_by`) values ('App\\Models\\Order', 1016, '{\\\"total_price\\\":0,\\\"commission_fee\\\":0,\\\"tips\\\":0,\\\"address\\\":\\\"{\\\\\"address\\\\\":\\\\\"Rua 3, 34 - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\\\\u00f3polis - GO, 75860-000, Brasil\\\\\",\\\\\"office\\\\\":null,\\\\\"house\\\\\":null,\\\\\"floor\\\\\":null}\\\",\\\"location\\\":\\\"{\\\\\"latitude\\\\\":\\\\\"-18.439344666666663\\\\\",\\\\\"longitude\\\\\":\\\\\"-50.43331116666666\\\\\"}\\\"}', 'order_updated', '2025-07-26 11:15:12', 101)", "type": "query", "params": [], "bindings": ["App\\Models\\Order", "1016", "{&quot;total_price&quot;:0,&quot;commission_fee&quot;:0,&quot;tips&quot;:0,&quot;address&quot;:&quot;{\\&quot;address\\&quot;:\\&quot;Rua 3, 34 - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\\\\u00f3polis - GO, 75860-000, Brasil\\&quot;,\\&quot;office\\&quot;:null,\\&quot;house\\&quot;:null,\\&quot;floor\\&quot;:null}&quot;,&quot;location&quot;:&quot;{\\&quot;latitude\\&quot;:\\&quot;-18.439344666666663\\&quot;,\\&quot;longitude\\&quot;:\\&quot;-50.43331116666666\\&quot;}&quot;}", "order_updated", "2025-07-26 11:15:12", "101"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\ModelLogService\\ModelLogService.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\app\\Observers\\OrderObserver.php", "line": 48}, {"index": 29, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 419}, {"index": 30, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}, {"index": 34, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Services\\ModelLogService\\ModelLogService.php:30", "connection": "foodyman", "start_percent": 83.834, "width_percent": 0.622}, {"sql": "select * from `settings` where `key` = 'by_subscription' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["by_subscription"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 432}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 253}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00026000000000000003, "duration_str": "260μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:432", "connection": "foodyman", "start_percent": 84.456, "width_percent": 0.449}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 226}, {"index": 9, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 10, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 721}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 128}, {"index": 16, "namespace": "middleware", "name": "role", "line": 30}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 18, "namespace": "middleware", "name": "sanctum.check", "line": 18}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 20, "namespace": "middleware", "name": "block.ip", "line": 24}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Middleware\\TrustLicence.php", "line": 65}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 26, "namespace": "middleware", "name": "throttle", "line": 127}, {"index": 27, "namespace": "middleware", "name": "throttle", "line": 58}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 103}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 719}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 698}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 662}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 651}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 167}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 128}, {"index": 37, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 41, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}, {"index": 42, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 40}, {"index": 43, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php", "line": 27}, {"index": 45, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 46, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 86}, {"index": 47, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 48, "namespace": null, "name": "\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php", "line": 52}, {"index": 49, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0, "duration_str": "", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:226", "connection": "foodyman", "start_percent": 84.905, "width_percent": 0}, {"sql": "select * from `orders` where `id` = 1016 limit 1", "type": "query", "params": [], "bindings": ["1016"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 84.905, "width_percent": 0.708}, {"sql": "select `users`.*, (select count(*) from `orders` where `users`.`id` = `orders`.`user_id` and `status` = 'delivered') as `orders_count`, (select sum(`orders`.`total_price`) from `orders` where `users`.`id` = `orders`.`user_id` and `status` = 'delivered') as `orders_sum_total_price` from `users` where `users`.`id` in (109)", "type": "query", "params": [], "bindings": ["delivered", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 85.613, "width_percent": 1.244}, {"sql": "select * from `reviews` where `reviews`.`reviewable_id` in (1016) and `reviews`.`reviewable_type` = 'App\\Models\\Order' and `reviews`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Order"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 86.857, "width_percent": 0.553}, {"sql": "select * from `point_histories` where `point_histories`.`order_id` in (1016) and `point_histories`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 87.409, "width_percent": 0.466}, {"sql": "select * from `currencies` where `currencies`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 87.876, "width_percent": 0.466}, {"sql": "select `users`.*, (select avg(`reviews`.`rating`) from `reviews` where `users`.`id` = `reviews`.`assignable_id` and `reviews`.`assignable_type` = 'App\\Models\\User' and `reviews`.`deleted_at` is null) as `assign_reviews_avg_rating` from `users` where 0 = 1", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 88.342, "width_percent": 0.57}, {"sql": "select * from `order_coupons` where `order_coupons`.`order_id` in (1016)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00026000000000000003, "duration_str": "260μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 88.912, "width_percent": 0.449}, {"sql": "select `id`, `location`, `tax`, `price`, `price_per_km`, `background_img`, `logo_img`, `uuid`, `phone` from `shops` where `shops`.`id` in (501)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 89.361, "width_percent": 0.466}, {"sql": "select * from `shop_translations` where `shop_translations`.`shop_id` in (501) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `shop_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 89.827, "width_percent": 0.587}, {"sql": "select * from `order_details` where `order_details`.`order_id` in (1016) and `parent_id` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 90.415, "width_percent": 0.674}, {"sql": "select * from `stocks` where `stocks`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00025, "duration_str": "250μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 91.088, "width_percent": 0.432}, {"sql": "select * from `products` where `products`.`id` in (2) and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 31, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 91.52, "width_percent": 0.553}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` in (2) and `start` <= '2025-07-26 00:00:00' and `end` >= '2025-07-26 00:00:00' and `active` = 1 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-07-26 00:00:00", "2025-07-26 00:00:00", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 35, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 36, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 37, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 92.073, "width_percent": 0.794}, {"sql": "select * from `units` where `units`.`id` in (1) and `units`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 36, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 37, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 92.867, "width_percent": 0.501}, {"sql": "select * from `unit_translations` where `unit_translations`.`unit_id` in (1) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `unit_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 41, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 42, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 43, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 45, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 93.368, "width_percent": 0.535}, {"sql": "select * from `product_translations` where `product_translations`.`product_id` in (2) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `product_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 36, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 37, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 93.903, "width_percent": 0.501}, {"sql": "select `extra_values`.*, `stock_extras`.`stock_id` as `pivot_stock_id`, `stock_extras`.`extra_value_id` as `pivot_extra_value_id` from `extra_values` inner join `stock_extras` on `extra_values`.`id` = `stock_extras`.`extra_value_id` where `stock_extras`.`stock_id` in (3) and `extra_values`.`deleted_at` is null order by `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 30, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 31, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 94.404, "width_percent": 0.518}, {"sql": "select * from `order_details` where `order_details`.`parent_id` in (520)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00026000000000000003, "duration_str": "260μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 94.922, "width_percent": 0.449}, {"sql": "select * from `order_refunds` where `order_refunds`.`order_id` in (1016)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 95.371, "width_percent": 0.466}, {"sql": "select * from `transactions` where `type` = 'model' and `parent_id` is null and `transactions`.`payable_id` in (1016) and `transactions`.`payable_type` = 'App\\Models\\Order' and `transactions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["model", "App\\Models\\Order"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 95.838, "width_percent": 0.708}, {"sql": "select * from `payments` where `payments`.`id` in (1) and `payments`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00026000000000000003, "duration_str": "260μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 96.546, "width_percent": 0.449}, {"sql": "select * from `galleries` where `galleries`.`loadable_id` in (1016) and `galleries`.`loadable_type` = 'App\\Models\\Order'", "type": "query", "params": [], "bindings": ["App\\Models\\Order"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 96.995, "width_percent": 0.553}, {"sql": "select * from `user_addresses` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 261}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\Admin\\OrderController.php", "line": 218}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00023, "duration_str": "230μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:261", "connection": "foodyman", "start_percent": 97.547, "width_percent": 0.397}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 501 and `orders`.`shop_id` is not null and (`shop_id` = 501 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["501", "501", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 97.945, "width_percent": 0.881}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 501 and `orders`.`shop_id` is not null and (`shop_id` = 501 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["501", "501", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 98.826, "width_percent": 0.553}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 109 and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["109", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\User.php", "line": 212}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Resources\\UserResource.php", "line": 46}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Models\\User.php:212", "connection": "foodyman", "start_percent": 99.378, "width_percent": 0.622}]}, "models": {"data": {"App\\Models\\Payment": 1, "App\\Models\\ProductTranslation": 1, "App\\Models\\Unit": 1, "App\\Models\\ShopTranslation": 1, "App\\Models\\Transaction": 2, "App\\Models\\Product": 3, "App\\Models\\Stock": 4, "App\\Models\\OrderDetail": 3, "App\\Models\\Order": 3, "App\\Models\\Shop": 4, "Spatie\\Permission\\Models\\Role": 2, "App\\Models\\User": 5, "Laravel\\Sanctum\\PersonalAccessToken": 1, "App\\Models\\Currency": 12, "App\\Models\\Language": 9}, "count": 52}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f7bc4c8-f2df-45d7-9437-2cdf1586a373\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/dashboard/admin/orders/1016", "status_code": "<pre class=sf-dump id=sf-dump-710402181 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-710402181\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1801947343 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1801947343\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-374001056 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>109</span>\n  \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"66 characters\">Rua 3, 34 - <PERSON><PERSON><PERSON>, Quirin&#243;polis - GO, 75860-000, Brasil</span>\"\n    \"<span class=sf-dump-key>office</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>house</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>floor</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"19 characters\">-18.439344666666663</span>\"\n    \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"18 characters\">-50.43331116666666</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>delivery_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-28</span>\"\n  \"<span class=sf-dump-key>delivery_time</span>\" => \"<span class=sf-dump-str title=\"5 characters\">14:05</span>\"\n  \"<span class=sf-dump-key>shop_id</span>\" => <span class=sf-dump-num>501</span>\n  \"<span class=sf-dump-key>currency_id</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>payment_type</span>\" => \"<span class=sf-dump-str title=\"13 characters\">cash_delivery</span>\"\n  \"<span class=sf-dump-key>delivery_type</span>\" => \"<span class=sf-dump-str title=\"8 characters\">delivery</span>\"\n  \"<span class=sf-dump-key>products</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>stock_id</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>receipt_discount</span>\" => <span class=sf-dump-num>0</span>\n  \"<span class=sf-dump-key>receipt_count</span>\" => <span class=sf-dump-num>0</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-374001056\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-474211557 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">408</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">Bearer 17|FH6uQTWGw4Xm8tmd1E7co373XRHNKxVuRMDNZJSm</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,it;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-474211557\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-871674444 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">63006</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.26 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"35 characters\">/api/v1/dashboard/admin/orders/1016</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"52 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"35 characters\">/api/v1/dashboard/admin/orders/1016</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"45 characters\">/index.php/api/v1/dashboard/admin/orders/1016</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">408</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">408</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_AUTHORIZATION</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Bearer 17|FH6uQTWGw4Xm8tmd1E7co373XRHNKxVuRMDNZJSm</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"44 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,it;q=0.6</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753539312.4271</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753539312</span>\n  \"<span class=sf-dump-key>argv</span>\" => []\n  \"<span class=sf-dump-key>argc</span>\" => <span class=sf-dump-num>0</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-871674444\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-420547777 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-420547777\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1954603193 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 26 Jul 2025 14:15:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5000</span>\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>4991</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1954603193\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2061852551 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2061852551\", {\"maxDepth\":0})</script>\n"}}