{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\seller-views\\\\users\\\\components\\\\form\\\\edit\\\\edit.js\",\n  _s = $RefreshSig$();\nimport { Card, Tabs } from 'antd';\nimport { useTranslation } from 'react-i18next';\nimport { useEffect, useState } from 'react';\nimport { useQueryParams } from 'helpers/useQueryParams';\nimport userService from 'services/seller/user';\nimport createImage from 'helpers/createImage';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { useParams } from 'react-router-dom';\nimport useDidUpdate from 'helpers/useDidUpdate';\nimport { disableRefetch } from 'redux/slices/menu';\nimport moment from 'configs/moment-config';\nimport UserEditDetails from './components/details';\nimport UserEditDeliverymanZone from './components/deliveryman-zone';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  TabPane\n} = Tabs;\nconst ShopUsersEdit = () => {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const queryParams = useQueryParams();\n  const dispatch = useDispatch();\n  const {\n    uuid\n  } = useParams();\n  const {\n    activeMenu\n  } = useSelector(state => state.menu, shallowEqual);\n  const [currentTab, setCurrentTab] = useState(queryParams.get('tab') || 'details');\n  const [userData, setUserData] = useState({});\n  const [loading, setLoading] = useState(false);\n  useEffect(() => {\n    fetch();\n    // eslint-disable-next-line\n  }, [uuid]);\n  useDidUpdate(() => {\n    if (activeMenu.refetch) {\n      fetch();\n    }\n  }, [activeMenu.refetch]);\n  const fetchUser = uuid => {\n    setLoading(true);\n    userService.getById(uuid).then(res => {\n      var _res$data, _res$data2, _res$data3, _res$data4;\n      const body = {\n        ...(res === null || res === void 0 ? void 0 : res.data),\n        avatar: res !== null && res !== void 0 && (_res$data = res.data) !== null && _res$data !== void 0 && _res$data.img ? [createImage(res === null || res === void 0 ? void 0 : (_res$data2 = res.data) === null || _res$data2 === void 0 ? void 0 : _res$data2.img)] : [],\n        birthday: res !== null && res !== void 0 && (_res$data3 = res.data) !== null && _res$data3 !== void 0 && _res$data3.birthday ? moment(res === null || res === void 0 ? void 0 : (_res$data4 = res.data) === null || _res$data4 === void 0 ? void 0 : _res$data4.birthday) : null\n      };\n      setUserData(body);\n    }).finally(() => {\n      setLoading(false);\n    });\n  };\n  const fetch = () => {\n    if (uuid) {\n      fetchUser(uuid);\n      dispatch(disableRefetch(activeMenu));\n    }\n  };\n  const handleChangeTab = tab => {\n    setCurrentTab(tab);\n    queryParams.set('tab', tab);\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    title: t('edit.user'),\n    children: /*#__PURE__*/_jsxDEV(Tabs, {\n      activeKey: currentTab,\n      onChange: handleChangeTab,\n      tabPosition: \"left\",\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(TabPane, {\n        tab: t('edit.user'),\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          loading: loading,\n          children: /*#__PURE__*/_jsxDEV(UserEditDetails, {\n            data: userData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)\n      }, 'details', false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), (userData === null || userData === void 0 ? void 0 : userData.role) === 'deliveryman' && /*#__PURE__*/_jsxDEV(TabPane, {\n        tab: t('deliveryman.zone'),\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          loading: loading,\n          children: /*#__PURE__*/_jsxDEV(UserEditDeliverymanZone, {\n            data: userData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this)\n      }, 'deliverymanzone', false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_s(ShopUsersEdit, \"JzaQ+BMtg3qb7I/UukjcAVoyssE=\", false, function () {\n  return [useTranslation, useQueryParams, useDispatch, useParams, useSelector, useDidUpdate];\n});\n_c = ShopUsersEdit;\nexport default ShopUsersEdit;\nvar _c;\n$RefreshReg$(_c, \"ShopUsersEdit\");", "map": {"version": 3, "names": ["Card", "Tabs", "useTranslation", "useEffect", "useState", "useQueryParams", "userService", "createImage", "shallowEqual", "useDispatch", "useSelector", "useParams", "useDidUpdate", "disable<PERSON><PERSON><PERSON><PERSON>", "moment", "UserEditDetails", "UserEditDeliverymanZone", "jsxDEV", "_jsxDEV", "TabPane", "ShopUsersEdit", "_s", "t", "queryParams", "dispatch", "uuid", "activeMenu", "state", "menu", "currentTab", "setCurrentTab", "get", "userData", "setUserData", "loading", "setLoading", "fetch", "refetch", "fetchUser", "getById", "then", "res", "_res$data", "_res$data2", "_res$data3", "_res$data4", "body", "data", "avatar", "img", "birthday", "finally", "handleChangeTab", "tab", "set", "title", "children", "active<PERSON><PERSON>", "onChange", "tabPosition", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/seller-views/users/components/form/edit/edit.js"], "sourcesContent": ["import { Card, Tabs } from 'antd';\nimport { useTranslation } from 'react-i18next';\nimport { useEffect, useState } from 'react';\nimport { useQueryParams } from 'helpers/useQueryParams';\nimport userService from 'services/seller/user';\nimport createImage from 'helpers/createImage';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { useParams } from 'react-router-dom';\nimport useDidUpdate from 'helpers/useDidUpdate';\nimport { disableRefetch } from 'redux/slices/menu';\nimport moment from 'configs/moment-config';\nimport UserEditDetails from './components/details';\nimport UserEditDeliverymanZone from './components/deliveryman-zone';\n\nconst { TabPane } = Tabs;\n\nconst ShopUsersEdit = () => {\n  const { t } = useTranslation();\n  const queryParams = useQueryParams();\n  const dispatch = useDispatch();\n  const { uuid } = useParams();\n\n  const { activeMenu } = useSelector((state) => state.menu, shallowEqual);\n\n  const [currentTab, setCurrentTab] = useState(\n    queryParams.get('tab') || 'details',\n  );\n  const [userData, setUserData] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    fetch();\n    // eslint-disable-next-line\n  }, [uuid]);\n\n  useDidUpdate(() => {\n    if (activeMenu.refetch) {\n      fetch();\n    }\n  }, [activeMenu.refetch]);\n\n  const fetchUser = (uuid) => {\n    setLoading(true);\n    userService\n      .getById(uuid)\n      .then((res) => {\n        const body = {\n          ...res?.data,\n          avatar: res?.data?.img ? [createImage(res?.data?.img)] : [],\n          birthday: res?.data?.birthday ? moment(res?.data?.birthday) : null,\n        };\n        setUserData(body);\n      })\n      .finally(() => {\n        setLoading(false);\n      });\n  };\n\n  const fetch = () => {\n    if (uuid) {\n      fetchUser(uuid);\n      dispatch(disableRefetch(activeMenu));\n    }\n  };\n\n  const handleChangeTab = (tab) => {\n    setCurrentTab(tab);\n    queryParams.set('tab', tab);\n  };\n\n  return (\n    <Card title={t('edit.user')}>\n      <Tabs\n        activeKey={currentTab}\n        onChange={handleChangeTab}\n        tabPosition='left'\n        size='small'\n      >\n        <TabPane key='details' tab={t('edit.user')}>\n          <Card loading={loading}>\n            <UserEditDetails data={userData} />\n          </Card>\n        </TabPane>\n        {userData?.role === 'deliveryman' && (\n          <TabPane key='deliverymanzone' tab={t('deliveryman.zone')}>\n            <Card loading={loading}>\n              <UserEditDeliverymanZone data={userData} />\n            </Card>\n          </TabPane>\n        )}\n      </Tabs>\n    </Card>\n  );\n};\n\nexport default ShopUsersEdit;\n"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,IAAI,QAAQ,MAAM;AACjC,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,cAAc,QAAQ,wBAAwB;AACvD,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,SAASC,cAAc,QAAQ,mBAAmB;AAClD,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,eAAe,MAAM,sBAAsB;AAClD,OAAOC,uBAAuB,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,MAAM;EAAEC;AAAQ,CAAC,GAAGlB,IAAI;AAExB,MAAMmB,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC;EAAE,CAAC,GAAGpB,cAAc,CAAC,CAAC;EAC9B,MAAMqB,WAAW,GAAGlB,cAAc,CAAC,CAAC;EACpC,MAAMmB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgB;EAAK,CAAC,GAAGd,SAAS,CAAC,CAAC;EAE5B,MAAM;IAAEe;EAAW,CAAC,GAAGhB,WAAW,CAAEiB,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAEpB,YAAY,CAAC;EAEvE,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAC1CmB,WAAW,CAACQ,GAAG,CAAC,KAAK,CAAC,IAAI,SAC5B,CAAC;EACD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAE7CD,SAAS,CAAC,MAAM;IACdiC,KAAK,CAAC,CAAC;IACP;EACF,CAAC,EAAE,CAACX,IAAI,CAAC,CAAC;EAEVb,YAAY,CAAC,MAAM;IACjB,IAAIc,UAAU,CAACW,OAAO,EAAE;MACtBD,KAAK,CAAC,CAAC;IACT;EACF,CAAC,EAAE,CAACV,UAAU,CAACW,OAAO,CAAC,CAAC;EAExB,MAAMC,SAAS,GAAIb,IAAI,IAAK;IAC1BU,UAAU,CAAC,IAAI,CAAC;IAChB7B,WAAW,CACRiC,OAAO,CAACd,IAAI,CAAC,CACbe,IAAI,CAAEC,GAAG,IAAK;MAAA,IAAAC,SAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAC,UAAA;MACb,MAAMC,IAAI,GAAG;QACX,IAAGL,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEM,IAAI;QACZC,MAAM,EAAEP,GAAG,aAAHA,GAAG,gBAAAC,SAAA,GAAHD,GAAG,CAAEM,IAAI,cAAAL,SAAA,eAATA,SAAA,CAAWO,GAAG,GAAG,CAAC1C,WAAW,CAACkC,GAAG,aAAHA,GAAG,wBAAAE,UAAA,GAAHF,GAAG,CAAEM,IAAI,cAAAJ,UAAA,uBAATA,UAAA,CAAWM,GAAG,CAAC,CAAC,GAAG,EAAE;QAC3DC,QAAQ,EAAET,GAAG,aAAHA,GAAG,gBAAAG,UAAA,GAAHH,GAAG,CAAEM,IAAI,cAAAH,UAAA,eAATA,UAAA,CAAWM,QAAQ,GAAGpC,MAAM,CAAC2B,GAAG,aAAHA,GAAG,wBAAAI,UAAA,GAAHJ,GAAG,CAAEM,IAAI,cAAAF,UAAA,uBAATA,UAAA,CAAWK,QAAQ,CAAC,GAAG;MAChE,CAAC;MACDjB,WAAW,CAACa,IAAI,CAAC;IACnB,CAAC,CAAC,CACDK,OAAO,CAAC,MAAM;MACbhB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,KAAK,GAAGA,CAAA,KAAM;IAClB,IAAIX,IAAI,EAAE;MACRa,SAAS,CAACb,IAAI,CAAC;MACfD,QAAQ,CAACX,cAAc,CAACa,UAAU,CAAC,CAAC;IACtC;EACF,CAAC;EAED,MAAM0B,eAAe,GAAIC,GAAG,IAAK;IAC/BvB,aAAa,CAACuB,GAAG,CAAC;IAClB9B,WAAW,CAAC+B,GAAG,CAAC,KAAK,EAAED,GAAG,CAAC;EAC7B,CAAC;EAED,oBACEnC,OAAA,CAAClB,IAAI;IAACuD,KAAK,EAAEjC,CAAC,CAAC,WAAW,CAAE;IAAAkC,QAAA,eAC1BtC,OAAA,CAACjB,IAAI;MACHwD,SAAS,EAAE5B,UAAW;MACtB6B,QAAQ,EAAEN,eAAgB;MAC1BO,WAAW,EAAC,MAAM;MAClBC,IAAI,EAAC,OAAO;MAAAJ,QAAA,gBAEZtC,OAAA,CAACC,OAAO;QAAekC,GAAG,EAAE/B,CAAC,CAAC,WAAW,CAAE;QAAAkC,QAAA,eACzCtC,OAAA,CAAClB,IAAI;UAACkC,OAAO,EAAEA,OAAQ;UAAAsB,QAAA,eACrBtC,OAAA,CAACH,eAAe;YAACgC,IAAI,EAAEf;UAAS;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B;MAAC,GAHI,SAAS;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIb,CAAC,EACT,CAAAhC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEiC,IAAI,MAAK,aAAa,iBAC/B/C,OAAA,CAACC,OAAO;QAAuBkC,GAAG,EAAE/B,CAAC,CAAC,kBAAkB,CAAE;QAAAkC,QAAA,eACxDtC,OAAA,CAAClB,IAAI;UAACkC,OAAO,EAAEA,OAAQ;UAAAsB,QAAA,eACrBtC,OAAA,CAACF,uBAAuB;YAAC+B,IAAI,EAAEf;UAAS;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC,GAHI,iBAAiB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIrB,CACV;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAAC3C,EAAA,CA7EID,aAAa;EAAA,QACHlB,cAAc,EACRG,cAAc,EACjBI,WAAW,EACXE,SAAS,EAEHD,WAAW,EAalCE,YAAY;AAAA;AAAAsD,EAAA,GAnBR9C,aAAa;AA+EnB,eAAeA,aAAa;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}