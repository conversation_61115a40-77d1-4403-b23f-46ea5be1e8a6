{"__meta": {"id": "X765913643d0f86d6c0ded4de16565da5", "datetime": "2025-07-26 13:00:37", "utime": 1753545637.823312, "method": "POST", "uri": "/api/v1/dashboard/user/cart/insert-product?lang=pt-BR", "ip": "127.0.0.1"}, "php": {"version": "8.2.26", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[13:00:37] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753545637.58839, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753545637.357245, "end": 1753545637.823333, "duration": 0.46608805656433105, "duration_str": "466ms", "measures": [{"label": "Booting", "start": 1753545637.357245, "relative_start": 0, "end": 1753545637.573113, "relative_end": 1753545637.573113, "duration": 0.2158679962158203, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753545637.573121, "relative_start": 0.21587610244750977, "end": 1753545637.823338, "relative_end": 5.0067901611328125e-06, "duration": 0.2502169609069824, "duration_str": "250ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 45161992, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/dashboard/user/cart/insert-product", "middleware": "api, block.ip, sanctum.check", "controller": "App\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController@insertProducts", "as": "user.", "namespace": null, "prefix": "api/v1/dashboard/user", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\OSPanel\\home\\api.ticketflow.chat\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php&line=179\">\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php:179-191</a>"}, "queries": {"nb_statements": 64, "nb_failed_statements": 0, "accumulated_duration": 0.049519999999999995, "accumulated_duration_str": "49.52ms", "statements": [{"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 51}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.0246, "duration_str": "24.6ms", "stmt_id": "\\app\\Repositories\\CoreRepository.php:51", "connection": "foodyman", "start_percent": 0, "width_percent": 49.677}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 25}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:41", "connection": "foodyman", "start_percent": 49.677, "width_percent": 0.909}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 50.586, "width_percent": 0.626}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 51.212, "width_percent": 0.646}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '30' limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 64}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php:64", "connection": "foodyman", "start_percent": 51.858, "width_percent": 1.05}, {"sql": "select * from `users` where `users`.`id` = 111 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["111"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 137}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:137", "connection": "foodyman", "start_percent": 52.908, "width_percent": 0.848}, {"sql": "select count(*) as aggregate from `shops` where `id` = 501 and `deleted_at` is null", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.0005600000000000001, "duration_str": "560μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 53.756, "width_percent": 1.131}, {"sql": "select count(*) as aggregate from `currencies` where `id` = 1 and `deleted_at` is null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 54.887, "width_percent": 0.687}, {"sql": "select count(*) as aggregate from `stocks` where `id` = 7 and `deleted_at` is null", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 55.574, "width_percent": 0.687}, {"sql": "select * from `currencies` where `currencies`.`id` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 605}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:605", "connection": "foodyman", "start_percent": 56.26, "width_percent": 0.707}, {"sql": "select `id`, `shop_id`, `owner_id` from `carts` where `owner_id` = 111 limit 1", "type": "query", "params": [], "bindings": ["111"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 608}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:608", "connection": "foodyman", "start_percent": 56.967, "width_percent": 0.606}, {"sql": "select * from `carts` where (`owner_id` = 111 and `shop_id` = 501) limit 1", "type": "query", "params": [], "bindings": ["111", "501"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 627}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:627", "connection": "foodyman", "start_percent": 57.573, "width_percent": 0.929}, {"sql": "select * from `shops` where `shops`.`id` in (501) and `shops`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 627}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:627", "connection": "foodyman", "start_percent": 58.502, "width_percent": 0.646}, {"sql": "select * from `user_carts` where `user_carts`.`cart_id` in (20)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 627}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:627", "connection": "foodyman", "start_percent": 59.148, "width_percent": 0.687}, {"sql": "select * from `cart_details` where `cart_details`.`user_cart_id` in (20) and `parent_id` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 627}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:627", "connection": "foodyman", "start_percent": 59.834, "width_percent": 0.626}, {"sql": "select * from `stocks` where `stocks`.`id` in (7) and `stocks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 31, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 627}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:627", "connection": "foodyman", "start_percent": 60.46, "width_percent": 0.545}, {"sql": "select `id`, `status`, `shop_id`, `active`, `min_qty`, `max_qty`, `tax`, `img`, `interval` from `products` where `products`.`id` in (2) and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 36, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 627}, {"index": 37, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00025, "duration_str": "250μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:627", "connection": "foodyman", "start_percent": 61.006, "width_percent": 0.505}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` in (2) and `start` <= '2025-07-26 00:00:00' and `end` >= '2025-07-26 00:00:00' and `active` = 1 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-07-26 00:00:00", "2025-07-26 00:00:00", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 40, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 627}, {"index": 41, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 42, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 43, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:627", "connection": "foodyman", "start_percent": 61.511, "width_percent": 0.848}, {"sql": "select * from `cart_details` where `cart_details`.`parent_id` in (23)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 31, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 627}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:627", "connection": "foodyman", "start_percent": 62.359, "width_percent": 0.848}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (111) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 647}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:188", "connection": "foodyman", "start_percent": 63.207, "width_percent": 0.828}, {"sql": "select * from `user_carts` where `user_carts`.`cart_id` = 20 and `user_carts`.`cart_id` is not null and (`user_id` = 111 and `cart_id` = 20) limit 1", "type": "query", "params": [], "bindings": ["20", "111", "20"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 651}, {"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:651", "connection": "foodyman", "start_percent": 64.035, "width_percent": 1.07}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 10, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 11, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 721}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 128}, {"index": 19, "namespace": "middleware", "name": "sanctum.check", "line": 18}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 21, "namespace": "middleware", "name": "block.ip", "line": 24}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Middleware\\TrustLicence.php", "line": 65}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 27, "namespace": "middleware", "name": "throttle", "line": 127}, {"index": 28, "namespace": "middleware", "name": "throttle", "line": 58}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 103}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 719}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 698}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 662}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 651}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 167}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 128}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31}, {"index": 41, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 42, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}, {"index": 43, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 40}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 45, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php", "line": 27}, {"index": 46, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 47, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 86}, {"index": 48, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 49, "namespace": null, "name": "\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php", "line": 52}], "duration": 0, "duration_str": "", "stmt_id": "\\app\\Services\\CartService\\CartService.php:673", "connection": "foodyman", "start_percent": 65.105, "width_percent": 0}, {"sql": "delete from `cart_details` where `user_cart_id` = 20 and `bonus` = 1", "type": "query", "params": [], "bindings": ["20", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 678}, {"index": 14, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 15, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:678", "connection": "foodyman", "start_percent": 65.105, "width_percent": 0.949}, {"sql": "select * from `stocks` where exists (select * from `products` where `stocks`.`countable_id` = `products`.`id` and `status` = 'published' and `active` = 1 and `shop_id` = 501 and `products`.`deleted_at` is null) and `stocks`.`id` = 7 and `stocks`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["published", "1", "501", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 708}, {"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:708", "connection": "foodyman", "start_percent": 66.054, "width_percent": 0.868}, {"sql": "select `id`, `status`, `active`, `shop_id`, `min_qty`, `max_qty`, `tax`, `img`, `interval` from `products` where `products`.`id` in (2) and `status` = 'published' and `active` = 1 and `shop_id` = 501 and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["published", "1", "501"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 708}, {"index": 25, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 26, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 27, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:708", "connection": "foodyman", "start_percent": 66.922, "width_percent": 0.687}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` in (2) and `start` <= '2025-07-26 00:00:00' and `end` >= '2025-07-26 00:00:00' and `active` = 1 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-07-26 00:00:00", "2025-07-26 00:00:00", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 708}, {"index": 29, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 30, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 31, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:708", "connection": "foodyman", "start_percent": 67.609, "width_percent": 0.828}, {"sql": "select * from `units` where 0 = 1 and `units`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 708}, {"index": 30, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 31, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 32, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00023999999999999998, "duration_str": "240μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:708", "connection": "foodyman", "start_percent": 68.437, "width_percent": 0.485}, {"sql": "select * from `cart_details` where not exists (select * from `cart_details` as `laravel_reserved_0` where `cart_details`.`id` = `laravel_reserved_0`.`parent_id`) and (`user_cart_id` = 20 and `stock_id` = 7 and `parent_id` is null) limit 1", "type": "query", "params": [], "bindings": ["20", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 760}, {"index": 19, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:760", "connection": "foodyman", "start_percent": 68.922, "width_percent": 0.727}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'cart_details'", "type": "query", "params": [], "bindings": ["foodyman", "cart_details"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 764}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 19, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:764", "connection": "foodyman", "start_percent": 69.649, "width_percent": 1.817}, {"sql": "update `cart_details` set `quantity` = 2, `price` = 130, `cart_details`.`updated_at` = '2025-07-26 13:00:37' where `id` = 23", "type": "query", "params": [], "bindings": ["2", "130", "2025-07-26 13:00:37", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 764}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 19, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:764", "connection": "foodyman", "start_percent": 71.466, "width_percent": 0.848}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 9, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 10, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 721}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 128}, {"index": 18, "namespace": "middleware", "name": "sanctum.check", "line": 18}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 20, "namespace": "middleware", "name": "block.ip", "line": 24}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Middleware\\TrustLicence.php", "line": 65}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 26, "namespace": "middleware", "name": "throttle", "line": 127}, {"index": 27, "namespace": "middleware", "name": "throttle", "line": 58}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 103}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 719}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 698}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 662}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 651}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 167}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 128}, {"index": 37, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 41, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}, {"index": 42, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 40}, {"index": 43, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php", "line": 27}, {"index": 45, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 46, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 86}, {"index": 47, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 48, "namespace": null, "name": "\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php", "line": 52}, {"index": 49, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0, "duration_str": "", "stmt_id": "\\app\\Services\\CartService\\CartService.php:673", "connection": "foodyman", "start_percent": 72.314, "width_percent": 0}, {"sql": "select `id`, `total_price`, `shop_id` from `carts` where `carts`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": ["20"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1161}, {"index": 17, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1161", "connection": "foodyman", "start_percent": 72.314, "width_percent": 0.565}, {"sql": "select * from `user_carts` where `user_carts`.`cart_id` in (20)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1161}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1161", "connection": "foodyman", "start_percent": 72.88, "width_percent": 0.586}, {"sql": "select `id`, `user_cart_id`, `stock_id`, `price`, `discount`, `quantity`, `bonus_type` from `cart_details` where `cart_details`.`user_cart_id` in (20)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1161}, {"index": 27, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 28, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 29, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1161", "connection": "foodyman", "start_percent": 73.465, "width_percent": 0.606}, {"sql": "select * from `stocks` where `stocks`.`id` in (7) and `stocks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 31, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1161}, {"index": 32, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 33, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 34, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1161", "connection": "foodyman", "start_percent": 74.071, "width_percent": 0.727}, {"sql": "select * from `bonuses` where `bonuses`.`bonusable_id` in (7) and `bonuses`.`bonusable_type` = 'App\\Models\\Stock' and `expired_at` > '2025-07-26 13:00:37' and `bonuses`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Stock", "2025-07-26 13:00:37"], "hints": null, "show_copy": false, "backtrace": [{"index": 36, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1161}, {"index": 37, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 38, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 39, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1161", "connection": "foodyman", "start_percent": 74.798, "width_percent": 0.909}, {"sql": "select * from `shops` where `shops`.`id` in (501) and `shops`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1161}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1161", "connection": "foodyman", "start_percent": 75.707, "width_percent": 0.626}, {"sql": "select * from `bonuses` where `bonuses`.`bonusable_id` in (501) and `bonuses`.`bonusable_type` = 'App\\Models\\Shop' and `expired_at` > '2025-07-26 13:00:37' and `bonuses`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Shop", "2025-07-26 13:00:37"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1161}, {"index": 27, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 28, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 29, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1161", "connection": "foodyman", "start_percent": 76.333, "width_percent": 0.687}, {"sql": "delete from `cart_details` where `user_cart_id` in (20) and `bonus` = 1", "type": "query", "params": [], "bindings": ["20", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1215}, {"index": 11, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 12, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 13, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1215", "connection": "foodyman", "start_percent": 77.019, "width_percent": 0.767}, {"sql": "select * from `products` where `products`.`id` = 2 and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1318}, {"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1227}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 24, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1318", "connection": "foodyman", "start_percent": 77.787, "width_percent": 0.747}, {"sql": "select * from `bonuses` where `type` = 'count' and `bonusable_type` = 'App\\Models\\Stock' and `bonusable_id` = 7 and `value` <= 2 and `expired_at` >= '2025-07-26 13:00:37' and `bonuses`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["count", "App\\Models\\Stock", "7", "2", "2025-07-26 13:00:37"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1253}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 17, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1253", "connection": "foodyman", "start_percent": 78.534, "width_percent": 0.727}, {"sql": "select * from `receipts` where exists (select * from `stocks` inner join `receipt_stocks` on `stocks`.`id` = `receipt_stocks`.`stock_id` where `receipts`.`id` = `receipt_stocks`.`receipt_id` and `stocks`.`deleted_at` is null) and `shop_id` = 501 and `receipts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1350}, {"index": 15, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1264}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 17, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}], "duration": 0.0005600000000000001, "duration_str": "560μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1350", "connection": "foodyman", "start_percent": 79.261, "width_percent": 1.131}, {"sql": "select * from `carts` where `id` = 20 limit 1", "type": "query", "params": [], "bindings": ["20"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1266}, {"index": 17, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 19, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1266", "connection": "foodyman", "start_percent": 80.392, "width_percent": 0.868}, {"sql": "select * from `user_carts` where `user_carts`.`cart_id` in (20)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1266}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 24, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1266", "connection": "foodyman", "start_percent": 81.26, "width_percent": 0.565}, {"sql": "select * from `shops` where `shops`.`id` = 501 and `shops`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1277}, {"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1277", "connection": "foodyman", "start_percent": 81.826, "width_percent": 0.767}, {"sql": "select * from `bonuses` where `bonuses`.`bonusable_type` = 'App\\Models\\Shop' and `bonuses`.`bonusable_id` = 501 and `bonuses`.`bonusable_id` is not null and `bonuses`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Shop", "501"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1277}, {"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1277", "connection": "foodyman", "start_percent": 82.593, "width_percent": 0.626}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'carts'", "type": "query", "params": [], "bindings": ["foodyman", "carts"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1304}, {"index": 15, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 17, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1304", "connection": "foodyman", "start_percent": 83.219, "width_percent": 1.515}, {"sql": "update `carts` set `total_price` = 130, `carts`.`updated_at` = '2025-07-26 13:00:37' where `id` = 20", "type": "query", "params": [], "bindings": ["130", "2025-07-26 13:00:37", "20"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1304}, {"index": 15, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 17, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00109, "duration_str": "1.09ms", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1304", "connection": "foodyman", "start_percent": 84.733, "width_percent": 2.201}, {"sql": "select * from `carts` where `carts`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": ["20"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 17, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 86.935, "width_percent": 1.03}, {"sql": "select * from `shops` where `shops`.`id` in (501) and `shops`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 87.964, "width_percent": 0.727}, {"sql": "select * from `bonuses` where `bonuses`.`bonusable_id` in (501) and `bonuses`.`bonusable_type` = 'App\\Models\\Shop' and `expired_at` > '2025-07-26 13:00:37' and `status` = 1 and `bonuses`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Shop", "2025-07-26 13:00:37", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 27, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 28, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 29, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 88.691, "width_percent": 0.767}, {"sql": "select * from `user_carts` where `user_carts`.`cart_id` in (20)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00026000000000000003, "duration_str": "260μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 89.459, "width_percent": 0.525}, {"sql": "select * from `cart_details` where `cart_details`.`user_cart_id` in (20) and `parent_id` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 27, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 28, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 29, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 89.984, "width_percent": 0.626}, {"sql": "select * from `stocks` where `stocks`.`id` in (7) and `stocks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 31, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 32, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 33, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 34, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 90.61, "width_percent": 0.565}, {"sql": "select * from `bonuses` where `bonuses`.`bonusable_id` in (7) and `bonuses`.`bonusable_type` = 'App\\Models\\Stock' and `expired_at` > '2025-07-26 13:00:37' and `status` = 1 and `bonuses`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Stock", "2025-07-26 13:00:37", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 36, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 37, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 38, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 39, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 91.175, "width_percent": 0.586}, {"sql": "select * from `products` where `products`.`id` in (2) and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 36, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 37, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 38, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 39, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 91.761, "width_percent": 0.565}, {"sql": "select * from `units` where `units`.`id` in (2) and `units`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 41, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 42, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 43, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 44, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 45, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 92.326, "width_percent": 0.727}, {"sql": "select * from `unit_translations` where `unit_translations`.`unit_id` in (2) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `unit_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 46, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 47, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 48, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 49, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 93.053, "width_percent": 1.07}, {"sql": "select * from `product_translations` where `product_translations`.`product_id` in (2) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `product_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 41, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 42, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 43, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 44, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 45, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 94.124, "width_percent": 0.606}, {"sql": "select `extra_values`.*, `stock_extras`.`stock_id` as `pivot_stock_id`, `stock_extras`.`extra_value_id` as `pivot_extra_value_id` from `extra_values` inner join `stock_extras` on `extra_values`.`id` = `stock_extras`.`extra_value_id` where `stock_extras`.`stock_id` in (7) and `extra_values`.`deleted_at` is null order by `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 35, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 36, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 37, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 38, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 94.729, "width_percent": 0.606}, {"sql": "select * from `cart_details` where `cart_details`.`parent_id` in (23)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 31, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 32, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 33, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 34, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00025, "duration_str": "250μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 95.335, "width_percent": 0.505}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 501 and `orders`.`shop_id` is not null and (`shop_id` = 501 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["501", "501", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 95.84, "width_percent": 0.747}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 501 and `orders`.`shop_id` is not null and (`shop_id` = 501 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["501", "501", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 96.587, "width_percent": 0.989}, {"sql": "select * from `user_carts` where `user_carts`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": ["20"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\CartDetail.php", "line": 60}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Resources\\Cart\\CartDetailResource.php", "line": 25}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 59}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\Models\\CartDetail.php:60", "connection": "foodyman", "start_percent": 97.577, "width_percent": 1.01}, {"sql": "select * from `carts` where `carts`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": ["20"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\CartDetail.php", "line": 60}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Resources\\Cart\\CartDetailResource.php", "line": 25}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 59}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Models\\CartDetail.php:60", "connection": "foodyman", "start_percent": 98.586, "width_percent": 0.606}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` = 2 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 25, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 183}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Resources\\StockResource.php", "line": 32}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 99.192, "width_percent": 0.808}]}, "models": {"data": {"App\\Models\\ProductTranslation": 1, "App\\Models\\UnitTranslation": 1, "App\\Models\\Unit": 1, "Spatie\\Permission\\Models\\Role": 1, "App\\Models\\Product": 4, "App\\Models\\Stock": 4, "App\\Models\\CartDetail": 4, "App\\Models\\UserCart": 6, "App\\Models\\Shop": 4, "App\\Models\\Cart": 6, "App\\Models\\User": 1, "Laravel\\Sanctum\\PersonalAccessToken": 1, "App\\Models\\Currency": 3, "App\\Models\\Language": 2}, "count": 39}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f7bea7b-e849-402f-8466-6861a32f959f\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/dashboard/user/cart/insert-product", "status_code": "<pre class=sf-dump id=sf-dump-180979083 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-180979083\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-471924396 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"5 characters\">pt-BR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-471924396\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-445838112 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>shop_id</span>\" => <span class=sf-dump-num>501</span>\n  \"<span class=sf-dump-key>currency_id</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>rate</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>products</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>stock_id</span>\" => <span class=sf-dump-num>7</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>receipt_discount</span>\" => <span class=sf-dump-num>0</span>\n  \"<span class=sf-dump-key>receipt_count</span>\" => <span class=sf-dump-num>0</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-445838112\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-156013957 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">81</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">Bearer 30|tvXEmOWgkCv9xzLPD6ajFosDUNhRtNEj9x4OKwkr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-156013957\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-882929723 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59052</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.26 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"53 characters\">/api/v1/dashboard/user/cart/insert-product?lang=pt-BR</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"52 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"42 characters\">/api/v1/dashboard/user/cart/insert-product</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"52 characters\">/index.php/api/v1/dashboard/user/cart/insert-product</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"10 characters\">lang=pt-BR</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">81</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">81</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_AUTHORIZATION</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Bearer 30|tvXEmOWgkCv9xzLPD6ajFosDUNhRtNEj9x4OKwkr</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3001</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3001/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753545637.3572</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753545637</span>\n  \"<span class=sf-dump-key>argv</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">lang=pt-BR</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>argc</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-882929723\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-35390407 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-35390407\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-737726664 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 26 Jul 2025 16:00:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5000</span>\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>4969</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-737726664\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-50097517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-50097517\", {\"maxDepth\":0})</script>\n"}}