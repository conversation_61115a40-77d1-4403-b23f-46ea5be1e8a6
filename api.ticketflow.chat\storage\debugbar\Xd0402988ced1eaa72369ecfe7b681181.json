{"__meta": {"id": "Xd0402988ced1eaa72369ecfe7b681181", "datetime": "2025-07-26 13:59:27", "utime": 1753549167.622372, "method": "POST", "uri": "/api/v1/dashboard/user/orders?lang=pt-BR", "ip": "127.0.0.1"}, "php": {"version": "8.2.26", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[13:59:24] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753549164.968348, "xdebug_link": null, "collector": "log"}, {"message": "[13:59:25] LOG.error: 1 {\"type\":null,\"title\":\"1021\",\"body\":\"New order for you # 1021\",\"data\":{\"id\":1021,\"status\":\"accepted\",\"delivery_type\":\"delivery\"},\"sound\":\"default\"}", "message_html": null, "is_string": false, "label": "error", "time": 1753549165.577222, "xdebug_link": null, "collector": "log"}, {"message": "[13:59:27] LOG.warning: Implicit conversion from float 104.78 to int loses precision in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasRelationships.php on line 786", "message_html": null, "is_string": false, "label": "warning", "time": 1753549167.61067, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753549164.714763, "end": 1753549167.62239, "duration": 2.9076271057128906, "duration_str": "2.91s", "measures": [{"label": "Booting", "start": 1753549164.714763, "relative_start": 0, "end": 1753549164.949632, "relative_end": 1753549164.949632, "duration": 0.23486900329589844, "duration_str": "235ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753549164.949644, "relative_start": 0.23488116264343262, "end": 1753549167.622393, "relative_end": 2.86102294921875e-06, "duration": 2.6727488040924072, "duration_str": "2.67s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 48203320, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/dashboard/user/orders", "middleware": "api, block.ip, sanctum.check", "as": "user.orders.store", "controller": "App\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController@store", "namespace": null, "prefix": "api/v1/dashboard/user", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\OSPanel\\home\\api.ticketflow.chat\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php&line=65\">\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php:65-114</a>"}, "queries": {"nb_statements": 133, "nb_failed_statements": 0, "accumulated_duration": 0.10120999999999998, "accumulated_duration_str": "101ms", "statements": [{"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 51}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.028210000000000002, "duration_str": "28.21ms", "stmt_id": "\\app\\Repositories\\CoreRepository.php:51", "connection": "foodyman", "start_percent": 0, "width_percent": 27.873}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 25}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00061, "duration_str": "610μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:41", "connection": "foodyman", "start_percent": 27.873, "width_percent": 0.603}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 28.475, "width_percent": 0.889}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00062, "duration_str": "620μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 29.365, "width_percent": 0.613}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '30' limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 64}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php:64", "connection": "foodyman", "start_percent": 29.977, "width_percent": 0.534}, {"sql": "select * from `users` where `users`.`id` = 111 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["111"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 137}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:137", "connection": "foodyman", "start_percent": 30.511, "width_percent": 0.405}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-07-26 13:59:25', `personal_access_tokens`.`updated_at` = '2025-07-26 13:59:25' where `id` = 30", "type": "query", "params": [], "bindings": ["2025-07-26 13:59:25", "2025-07-26 13:59:25", "30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 19, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 21, "namespace": "middleware", "name": "block.ip", "line": 24}], "duration": 0.0015, "duration_str": "1.5ms", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:83", "connection": "foodyman", "start_percent": 30.916, "width_percent": 1.482}, {"sql": "select count(*) as aggregate from `payments` where `id` = 4 and `deleted_at` is null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 32.398, "width_percent": 0.583}, {"sql": "select count(*) as aggregate from `currencies` where `id` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 32.981, "width_percent": 0.445}, {"sql": "select count(*) as aggregate from `shops` where `id` = 501 and `deleted_at` is null", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 33.426, "width_percent": 0.464}, {"sql": "select count(*) as aggregate from `carts` where `id` = 21", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 33.89, "width_percent": 0.445}, {"sql": "select * from `settings` where `key` = 'order_auto_approved' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["order_auto_approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 69}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php:69", "connection": "foodyman", "start_percent": 34.335, "width_percent": 0.435}, {"sql": "select `id` from `carts` where `carts`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 80}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php:80", "connection": "foodyman", "start_percent": 34.769, "width_percent": 0.524}, {"sql": "select `id`, `cart_id` from `user_carts` where `user_carts`.`cart_id` in (21)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 80}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php:80", "connection": "foodyman", "start_percent": 35.293, "width_percent": 0.366}, {"sql": "select `id` from `cart_details` where `cart_details`.`user_cart_id` in (21)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 80}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php:80", "connection": "foodyman", "start_percent": 35.659, "width_percent": 0.267}, {"sql": "select `user_carts`.*, (select count(*) from `cart_details` where `user_carts`.`id` = `cart_details`.`user_cart_id`) as `cart_details_count` from `user_carts` where `user_carts`.`cart_id` = 21 and `user_carts`.`cart_id` is not null", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 97}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php:97", "connection": "foodyman", "start_percent": 35.925, "width_percent": 0.435}, {"sql": "select `phone` from `users` where `phone` is not null and `id` = 111 limit 1", "type": "query", "params": [], "bindings": ["111"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 956}, {"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 96}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:956", "connection": "foodyman", "start_percent": 36.36, "width_percent": 0.336}, {"sql": "select * from `settings` where `key` = 'before_order_phone_required' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["before_order_phone_required"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 959}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 96}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:959", "connection": "foodyman", "start_percent": 36.696, "width_percent": 0.277}, {"sql": "select * from `shops` where `shops`.`id` = 501 and `shops`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 103}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:103", "connection": "foodyman", "start_percent": 36.973, "width_percent": 0.484}, {"sql": "select exists(select * from `orders` where `cart_id` = 21) as `exists`", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 145}, {"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:145", "connection": "foodyman", "start_percent": 37.457, "width_percent": 0.316}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 10, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 721}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 128}, {"index": 17, "namespace": "middleware", "name": "sanctum.check", "line": 18}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 19, "namespace": "middleware", "name": "block.ip", "line": 24}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Middleware\\TrustLicence.php", "line": 65}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 25, "namespace": "middleware", "name": "throttle", "line": 127}, {"index": 26, "namespace": "middleware", "name": "throttle", "line": 58}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 103}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 719}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 698}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 662}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 651}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 167}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 128}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 37, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}, {"index": 41, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 40}, {"index": 42, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 43, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php", "line": 27}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 45, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 86}, {"index": 46, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 47, "namespace": null, "name": "\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php", "line": 52}, {"index": 48, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 49, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php", "line": 39}], "duration": 0, "duration_str": "", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:153", "connection": "foodyman", "start_percent": 37.773, "width_percent": 0}, {"sql": "select `id` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 752}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 156}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:752", "connection": "foodyman", "start_percent": 37.773, "width_percent": 0.455}, {"sql": "select * from `currencies` where `currencies`.`id` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 755}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 156}, {"index": 24, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.***************99996, "duration_str": "480μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:755", "connection": "foodyman", "start_percent": 38.227, "width_percent": 0.474}, {"sql": "select * from `settings` where `key` = 'service_fee' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["service_fee"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 772}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 156}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:772", "connection": "foodyman", "start_percent": 38.702, "width_percent": 0.455}, {"sql": "select * from `orders` where (`user_id` = 111 and `waiter_id` is null and `table_id` is null and `booking_id` is null and `user_booking_id` is null and `total_price` = 0 and `otp` = 5752 and `currency_id` = 1 and `rate` = 1 and `note` is null and `shop_id` = 501 and `phone` is null and `username` is null and `tax` = 0 and `commission_fee` = 0 and `service_fee` = 2 and `status` = 'accepted' and `delivery_fee` = 4.78 and `waiter_fee` = 0 and `delivery_type` = 'delivery' and `location` = '-18.***************' and `address` = 'Rua 3, 36 - <PERSON><PERSON><PERSON>, Quirinópolis - GO, 75860-000, Brasil' and `address_id` is null and `deliveryman` is null and `delivery_date` = '2025-07-26' and `delivery_time` = '15:30' and `total_discount` = 0 and `payment_method` is null and `change_required` = 0 and `change_amount` is null and `payment_notes` is null) limit 1", "type": "query", "params": [], "bindings": ["111", "0", "5752", "1", "1", "501", "0", "0", "2", "accepted", "4.78", "0", "delivery", "-18.***************", "Rua 3, 36 - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>&<PERSON>;polis - GO, 75860-000, Brasil", "2025-07-26", "15:30", "0", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 156}, {"index": 23, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.0008399999999999999, "duration_str": "840μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:156", "connection": "foodyman", "start_percent": 39.156, "width_percent": 0.83}, {"sql": "insert into `orders` (`user_id`, `waiter_id`, `table_id`, `booking_id`, `user_booking_id`, `total_price`, `otp`, `currency_id`, `rate`, `note`, `shop_id`, `phone`, `username`, `tax`, `commission_fee`, `service_fee`, `status`, `delivery_fee`, `waiter_fee`, `delivery_type`, `location`, `address`, `deliveryman`, `delivery_date`, `delivery_time`, `total_discount`, `payment_method`, `change_required`, `change_amount`, `payment_notes`, `updated_at`, `created_at`) values (111, '', '', '', '', 0, 5752, 1, 1, '', 501, '', '', 0, 0, 2, 'accepted', 4.78, 0, 'delivery', '{\\\"latitude\\\":\\\"-18.***************\\\",\\\"longitude\\\":\\\"-50.**************\\\"}', '{\\\"address\\\":\\\"Rua 3, 36 - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\\u00f3polis - GO, 75860-000, Brasil\\\",\\\"office\\\":null,\\\"house\\\":null,\\\"floor\\\":null}', '', '2025-07-26', '15:30', 0, '', 0, '', '', '2025-07-26 13:59:25', '2025-07-26 13:59:25')", "type": "query", "params": [], "bindings": ["111", "", "", "", "", "0", "5752", "1", "1", "", "501", "", "", "0", "0", "2", "accepted", "4.78", "0", "delivery", "{&quot;latitude&quot;:&quot;-18.***************&quot;,&quot;longitude&quot;:&quot;-50.**************&quot;}", "{&quot;address&quot;:&quot;Rua 3, 36 - <PERSON><PERSON><PERSON>, Q<PERSON>rin\\u00f3polis - GO, 75860-000, Brasil&quot;,&quot;office&quot;:null,&quot;house&quot;:null,&quot;floor&quot;:null}", "", "2025-07-26", "15:30", "0", "", "0", "", "", "2025-07-26 13:59:25", "2025-07-26 13:59:25"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 156}, {"index": 24, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:156", "connection": "foodyman", "start_percent": 39.986, "width_percent": 0.464}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\OrderObserver.php", "line": 33}, {"index": 28, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 156}, {"index": 32, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}], "duration": 0.0005600000000000001, "duration_str": "560μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 40.451, "width_percent": 0.553}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\OrderObserver.php", "line": 33}, {"index": 28, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 156}, {"index": 32, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 41.004, "width_percent": 0.336}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'model_logs'", "type": "query", "params": [], "bindings": ["foodyman", "model_logs"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\ModelLogService\\ModelLogService.php", "line": 30}, {"index": 21, "namespace": null, "name": "\\app\\Observers\\OrderObserver.php", "line": 33}, {"index": 32, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 156}, {"index": 36, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 37, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.0008399999999999999, "duration_str": "840μs", "stmt_id": "\\app\\Services\\ModelLogService\\ModelLogService.php:30", "connection": "foodyman", "start_percent": 41.34, "width_percent": 0.83}, {"sql": "insert into `model_logs` (`model_type`, `model_id`, `data`, `type`, `created_at`, `created_by`) values ('App\\Models\\Order', 1021, '{\\\"user_id\\\":111,\\\"waiter_id\\\":null,\\\"table_id\\\":null,\\\"booking_id\\\":null,\\\"user_booking_id\\\":null,\\\"total_price\\\":0,\\\"otp\\\":5752,\\\"currency_id\\\":1,\\\"rate\\\":1,\\\"note\\\":null,\\\"shop_id\\\":501,\\\"phone\\\":null,\\\"username\\\":null,\\\"tax\\\":0,\\\"commission_fee\\\":0,\\\"service_fee\\\":2,\\\"status\\\":\\\"accepted\\\",\\\"delivery_fee\\\":4.78,\\\"waiter_fee\\\":0,\\\"delivery_type\\\":\\\"delivery\\\",\\\"location\\\":\\\"{\\\\\"latitude\\\\\":\\\\\"-18.***************\\\\\",\\\\\"longitude\\\\\":\\\\\"-50.**************\\\\\"}\\\",\\\"address\\\":\\\"{\\\\\"address\\\\\":\\\\\"Rua 3, 36 - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\\\\u00f3polis - GO, 75860-000, Brasil\\\\\",\\\\\"office\\\\\":null,\\\\\"house\\\\\":null,\\\\\"floor\\\\\":null}\\\",\\\"deliveryman\\\":null,\\\"delivery_date\\\":\\\"2025-07-26\\\",\\\"delivery_time\\\":\\\"15:30\\\",\\\"total_discount\\\":0,\\\"payment_method\\\":null,\\\"change_required\\\":false,\\\"change_amount\\\":null,\\\"payment_notes\\\":null,\\\"updated_at\\\":\\\"2025-07-26 13:59:25\\\",\\\"created_at\\\":\\\"2025-07-26 13:59:25\\\",\\\"id\\\":1021}', 'order_created', '2025-07-26 13:59:25', 111)", "type": "query", "params": [], "bindings": ["App\\Models\\Order", "1021", "{&quot;user_id&quot;:111,&quot;waiter_id&quot;:null,&quot;table_id&quot;:null,&quot;booking_id&quot;:null,&quot;user_booking_id&quot;:null,&quot;total_price&quot;:0,&quot;otp&quot;:5752,&quot;currency_id&quot;:1,&quot;rate&quot;:1,&quot;note&quot;:null,&quot;shop_id&quot;:501,&quot;phone&quot;:null,&quot;username&quot;:null,&quot;tax&quot;:0,&quot;commission_fee&quot;:0,&quot;service_fee&quot;:2,&quot;status&quot;:&quot;accepted&quot;,&quot;delivery_fee&quot;:4.78,&quot;waiter_fee&quot;:0,&quot;delivery_type&quot;:&quot;delivery&quot;,&quot;location&quot;:&quot;{\\&quot;latitude\\&quot;:\\&quot;-18.***************\\&quot;,\\&quot;longitude\\&quot;:\\&quot;-50.**************\\&quot;}&quot;,&quot;address&quot;:&quot;{\\&quot;address\\&quot;:\\&quot;Rua 3, 36 - Jardim Primavera, Quirin\\\\u00f3polis - GO, 75860-000, Brasil\\&quot;,\\&quot;office\\&quot;:null,\\&quot;house\\&quot;:null,\\&quot;floor\\&quot;:null}&quot;,&quot;deliveryman&quot;:null,&quot;delivery_date&quot;:&quot;2025-07-26&quot;,&quot;delivery_time&quot;:&quot;15:30&quot;,&quot;total_discount&quot;:0,&quot;payment_method&quot;:null,&quot;change_required&quot;:false,&quot;change_amount&quot;:null,&quot;payment_notes&quot;:null,&quot;updated_at&quot;:&quot;2025-07-26 13:59:25&quot;,&quot;created_at&quot;:&quot;2025-07-26 13:59:25&quot;,&quot;id&quot;:1021}", "order_created", "2025-07-26 13:59:25", "111"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\ModelLogService\\ModelLogService.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\app\\Observers\\OrderObserver.php", "line": 33}, {"index": 33, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 156}, {"index": 37, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 38, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\app\\Services\\ModelLogService\\ModelLogService.php:30", "connection": "foodyman", "start_percent": 42.17, "width_percent": 0.514}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 42.684, "width_percent": 0.593}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 43.276, "width_percent": 0.316}, {"sql": "select `id`, `total_price`, `shop_id` from `carts` where `carts`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 134}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:134", "connection": "foodyman", "start_percent": 43.593, "width_percent": 0.277}, {"sql": "select * from `user_carts` where `user_carts`.`cart_id` in (21)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 134}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:134", "connection": "foodyman", "start_percent": 43.869, "width_percent": 0.336}, {"sql": "select `id`, `user_cart_id`, `stock_id`, `price`, `discount`, `quantity` from `cart_details` where `cart_details`.`user_cart_id` in (21)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 134}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 31, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00026000000000000003, "duration_str": "260μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:134", "connection": "foodyman", "start_percent": 44.205, "width_percent": 0.257}, {"sql": "select * from `stocks` where `stocks`.`id` in (7) and `stocks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 31, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 134}, {"index": 32, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 36, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 37, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:134", "connection": "foodyman", "start_percent": 44.462, "width_percent": 0.287}, {"sql": "select * from `bonuses` where `bonuses`.`bonusable_id` in (7) and `bonuses`.`bonusable_type` = 'App\\Models\\Stock' and `expired_at` > '2025-07-26 13:59:25' and `bonuses`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Stock", "2025-07-26 13:59:25"], "hints": null, "show_copy": false, "backtrace": [{"index": 36, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 134}, {"index": 37, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 41, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 42, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 43, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00062, "duration_str": "620μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:134", "connection": "foodyman", "start_percent": 44.749, "width_percent": 0.613}, {"sql": "select * from `shops` where `shops`.`id` in (501) and `shops`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 134}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:134", "connection": "foodyman", "start_percent": 45.361, "width_percent": 0.425}, {"sql": "select * from `bonuses` where `bonuses`.`bonusable_id` in (501) and `bonuses`.`bonusable_type` = 'App\\Models\\Shop' and `expired_at` > '2025-07-26 13:59:25' and `bonuses`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Shop", "2025-07-26 13:59:25"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 134}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 31, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:134", "connection": "foodyman", "start_percent": 45.786, "width_percent": 0.435}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 136}, {"index": 18, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 46.221, "width_percent": 0.415}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 136}, {"index": 18, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 46.636, "width_percent": 0.435}, {"sql": "delete from `cart_details` where `user_cart_id` in (21) and `bonus` = 1", "type": "query", "params": [], "bindings": ["21", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1215}, {"index": 11, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 136}, {"index": 12, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1215", "connection": "foodyman", "start_percent": 47.07, "width_percent": 0.405}, {"sql": "select * from `products` where `products`.`id` = 2 and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1318}, {"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1227}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 136}, {"index": 23, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1318", "connection": "foodyman", "start_percent": 47.476, "width_percent": 0.632}, {"sql": "select * from `bonuses` where `type` = 'count' and `bonusable_type` = 'App\\Models\\Stock' and `bonusable_id` = 7 and `value` <= 2 and `expired_at` >= '2025-07-26 13:59:25' and `bonuses`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["count", "App\\Models\\Stock", "7", "2", "2025-07-26 13:59:25"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1253}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 136}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1253", "connection": "foodyman", "start_percent": 48.108, "width_percent": 0.405}, {"sql": "select * from `receipts` where exists (select * from `stocks` inner join `receipt_stocks` on `stocks`.`id` = `receipt_stocks`.`stock_id` where `receipts`.`id` = `receipt_stocks`.`receipt_id` and `stocks`.`deleted_at` is null) and `shop_id` = 501 and `receipts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1350}, {"index": 15, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1264}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 136}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1350", "connection": "foodyman", "start_percent": 48.513, "width_percent": 0.682}, {"sql": "select * from `carts` where `id` = 21 limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1266}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 136}, {"index": 18, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1266", "connection": "foodyman", "start_percent": 49.195, "width_percent": 0.336}, {"sql": "select * from `user_carts` where `user_carts`.`cart_id` in (21)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1266}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 136}, {"index": 23, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1266", "connection": "foodyman", "start_percent": 49.531, "width_percent": 0.306}, {"sql": "select * from `shops` where `shops`.`id` = 501 and `shops`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1277}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 136}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1277", "connection": "foodyman", "start_percent": 49.837, "width_percent": 0.81}, {"sql": "select * from `bonuses` where `bonuses`.`bonusable_type` = 'App\\Models\\Shop' and `bonuses`.`bonusable_id` = 501 and `bonuses`.`bonusable_id` is not null and `bonuses`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Shop", "501"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1277}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 136}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1277", "connection": "foodyman", "start_percent": 50.647, "width_percent": 0.494}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'carts'", "type": "query", "params": [], "bindings": ["foodyman", "carts"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1304}, {"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 136}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1304", "connection": "foodyman", "start_percent": 51.141, "width_percent": 0.899}, {"sql": "select * from `carts` where `carts`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 143}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:143", "connection": "foodyman", "start_percent": 52.04, "width_percent": 0.484}, {"sql": "select * from `shops` where `shops`.`id` in (501) and `shops`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 143}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:143", "connection": "foodyman", "start_percent": 52.524, "width_percent": 0.415}, {"sql": "select * from `user_carts` where `user_carts`.`cart_id` in (21)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 143}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:143", "connection": "foodyman", "start_percent": 52.939, "width_percent": 0.375}, {"sql": "select * from `cart_details` where `cart_details`.`user_cart_id` in (21) and `parent_id` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 143}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 31, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:143", "connection": "foodyman", "start_percent": 53.315, "width_percent": 0.306}, {"sql": "select * from `stocks` where `stocks`.`id` in (7) and `stocks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 31, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 143}, {"index": 32, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 36, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 37, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:143", "connection": "foodyman", "start_percent": 53.621, "width_percent": 1.018}, {"sql": "select * from `products` where `products`.`id` in (2) and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 36, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 143}, {"index": 37, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 41, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 42, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 43, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:143", "connection": "foodyman", "start_percent": 54.639, "width_percent": 0.741}, {"sql": "select * from `cart_details` where `cart_details`.`parent_id` in (24)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 31, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 143}, {"index": 32, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 36, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 37, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:143", "connection": "foodyman", "start_percent": 55.38, "width_percent": 0.306}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` = 2 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 25, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 209}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 166}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 31, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 55.686, "width_percent": 0.425}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'order_details'", "type": "query", "params": [], "bindings": ["foodyman", "order_details"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 166}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:166", "connection": "foodyman", "start_percent": 56.111, "width_percent": 0.86}, {"sql": "insert into `order_details` (`note`, `origin_price`, `tax`, `discount`, `total_price`, `stock_id`, `parent_id`, `quantity`, `bonus`, `kitchen_id`, `order_id`, `updated_at`, `created_at`) values ('', 130, 0, 0, 130, 7, '', 2, 0, '', 1021, '2025-07-26 13:59:25', '2025-07-26 13:59:25')", "type": "query", "params": [], "bindings": ["", "130", "0", "0", "130", "7", "", "2", "0", "", "1021", "2025-07-26 13:59:25", "2025-07-26 13:59:25"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 166}, {"index": 19, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 23, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:166", "connection": "foodyman", "start_percent": 56.971, "width_percent": 0.514}, {"sql": "update `stocks` set `quantity` = `quantity` - 2 where `id` = 7 and `stocks`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 168}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0019199999999999998, "duration_str": "1.92ms", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:168", "connection": "foodyman", "start_percent": 57.484, "width_percent": 1.897}, {"sql": "delete from `cart_details` where `cart_details`.`stock_id` = 7 and `cart_details`.`stock_id` is not null", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Observers\\StockObserver.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 168}, {"index": 24, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 28, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 29, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.00151, "duration_str": "1.51ms", "stmt_id": "\\app\\Observers\\StockObserver.php:36", "connection": "foodyman", "start_percent": 59.381, "width_percent": 1.492}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\StockObserver.php", "line": 38}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 168}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}], "duration": 0.00133, "duration_str": "1.33ms", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 60.873, "width_percent": 1.314}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\StockObserver.php", "line": 38}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 168}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}], "duration": 0.00102, "duration_str": "1.02ms", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 62.188, "width_percent": 1.008}, {"sql": "insert into `model_logs` (`model_type`, `model_id`, `data`, `type`, `created_at`, `created_by`) values ('App\\Models\\Stock', 7, '{\\\"quantity\\\":95}', 'stock_updated', '2025-07-26 13:59:25', 111)", "type": "query", "params": [], "bindings": ["App\\Models\\Stock", "7", "{&quot;quantity&quot;:95}", "stock_updated", "2025-07-26 13:59:25", "111"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\ModelLogService\\ModelLogService.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\app\\Observers\\StockObserver.php", "line": 38}, {"index": 31, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 168}, {"index": 32, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 36, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\Services\\ModelLogService\\ModelLogService.php:30", "connection": "foodyman", "start_percent": 63.195, "width_percent": 0.534}, {"sql": "delete from `carts` where `id` = 21", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderDetailService.php", "line": 186}, {"index": 14, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 164}, {"index": 18, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\app\\Services\\OrderService\\OrderDetailService.php:186", "connection": "foodyman", "start_percent": 63.729, "width_percent": 0.672}, {"sql": "select * from `orders` where `id` = 1021 limit 1", "type": "query", "params": [], "bindings": ["1021"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 331}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:331", "connection": "foodyman", "start_percent": 64.401, "width_percent": 0.761}, {"sql": "select * from `order_details` where `order_details`.`order_id` in (1021)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 331}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:331", "connection": "foodyman", "start_percent": 65.162, "width_percent": 0.405}, {"sql": "select * from `transactions` where `type` = 'model' and `parent_id` is null and `transactions`.`payable_id` in (1021) and `transactions`.`payable_type` = 'App\\Models\\Order' and `transactions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["model", "App\\Models\\Order"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 331}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.***************99996, "duration_str": "480μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:331", "connection": "foodyman", "start_percent": 65.567, "width_percent": 0.474}, {"sql": "select * from `users` where `users`.`id` in (111)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 331}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:331", "connection": "foodyman", "start_percent": 66.041, "width_percent": 0.375}, {"sql": "select * from `order_coupons` where `order_coupons`.`order_id` in (1021)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 331}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.***************99996, "duration_str": "480μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:331", "connection": "foodyman", "start_percent": 66.416, "width_percent": 0.474}, {"sql": "select * from `shops` where `shops`.`id` in (501)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 331}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:331", "connection": "foodyman", "start_percent": 66.891, "width_percent": 0.464}, {"sql": "select * from `shop_subscriptions` where date(`expired_at`) >= '2025-07-26' and (`active` = 1) and `shop_subscriptions`.`shop_id` in (501) and `shop_subscriptions`.`deleted_at` is null order by `id` desc", "type": "query", "params": [], "bindings": ["2025-07-26", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 331}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}, {"index": 31, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:331", "connection": "foodyman", "start_percent": 67.355, "width_percent": 0.702}, {"sql": "select * from `stocks` where `stocks`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 920}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 340}, {"index": 23, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}, {"index": 27, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:920", "connection": "foodyman", "start_percent": 68.057, "width_percent": 0.306}, {"sql": "select * from `receipts` where exists (select * from `stocks` inner join `receipt_stocks` on `stocks`.`id` = `receipt_stocks`.`stock_id` where `receipts`.`id` = `receipt_stocks`.`receipt_id` and `stocks`.`deleted_at` is null) and `shop_id` = 501 and `receipts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 935}, {"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 340}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:935", "connection": "foodyman", "start_percent": 68.363, "width_percent": 0.464}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 937}, {"index": 18, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 340}, {"index": 19, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 68.827, "width_percent": 0.435}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 937}, {"index": 18, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 340}, {"index": 19, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 69.262, "width_percent": 0.336}, {"sql": "select * from `settings` where `key` = 'by_subscription' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["by_subscription"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 342}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:342", "connection": "foodyman", "start_percent": 69.598, "width_percent": 0.385}, {"sql": "select * from `settings` where `key` = 'service_fee' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["service_fee"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 343}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:343", "connection": "foodyman", "start_percent": 69.983, "width_percent": 0.435}, {"sql": "select * from `coupons` where `name` is null and `shop_id` = 501 and `qty` > 0 and date(`expired_at`) > '2025-07-26' and `coupons`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["501", "0", "2025-07-26"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 347}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:347", "connection": "foodyman", "start_percent": 70.418, "width_percent": 0.435}, {"sql": "update `orders` set `total_price` = 136.78, `commission_fee` = 6.839, `orders`.`updated_at` = '2025-07-26 13:59:25' where `id` = 1021", "type": "query", "params": [], "bindings": ["136.78", "6.839", "2025-07-26 13:59:25", "1021"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 419}, {"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}, {"index": 19, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:419", "connection": "foodyman", "start_percent": 70.853, "width_percent": 0.543}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\OrderObserver.php", "line": 48}, {"index": 24, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 419}, {"index": 25, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 71.396, "width_percent": 0.593}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Observers\\OrderObserver.php", "line": 48}, {"index": 24, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 419}, {"index": 25, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 71.989, "width_percent": 0.336}, {"sql": "insert into `model_logs` (`model_type`, `model_id`, `data`, `type`, `created_at`, `created_by`) values ('App\\Models\\Order', 1021, '{\\\"total_price\\\":0,\\\"commission_fee\\\":0,\\\"tips\\\":0,\\\"address\\\":\\\"{\\\\\"address\\\\\":\\\\\"Rua 3, 36 - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\\\\u00f3polis - GO, 75860-000, Brasil\\\\\",\\\\\"office\\\\\":null,\\\\\"house\\\\\":null,\\\\\"floor\\\\\":null}\\\",\\\"location\\\":\\\"{\\\\\"latitude\\\\\":\\\\\"-18.***************\\\\\",\\\\\"longitude\\\\\":\\\\\"-50.**************\\\\\"}\\\"}', 'order_updated', '2025-07-26 13:59:25', 111)", "type": "query", "params": [], "bindings": ["App\\Models\\Order", "1021", "{&quot;total_price&quot;:0,&quot;commission_fee&quot;:0,&quot;tips&quot;:0,&quot;address&quot;:&quot;{\\&quot;address\\&quot;:\\&quot;Rua 3, 36 - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\\\\u00f3polis - GO, 75860-000, Brasil\\&quot;,\\&quot;office\\&quot;:null,\\&quot;house\\&quot;:null,\\&quot;floor\\&quot;:null}&quot;,&quot;location&quot;:&quot;{\\&quot;latitude\\&quot;:\\&quot;-18.***************\\&quot;,\\&quot;longitude\\&quot;:\\&quot;-50.**************\\&quot;}&quot;}", "order_updated", "2025-07-26 13:59:25", "111"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\ModelLogService\\ModelLogService.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\app\\Observers\\OrderObserver.php", "line": 48}, {"index": 29, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 419}, {"index": 30, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}, {"index": 34, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "\\app\\Services\\ModelLogService\\ModelLogService.php:30", "connection": "foodyman", "start_percent": 72.325, "width_percent": 0.346}, {"sql": "select * from `settings` where `key` = 'by_subscription' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["by_subscription"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 432}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 169}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:432", "connection": "foodyman", "start_percent": 72.671, "width_percent": 0.306}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 175}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.***************99996, "duration_str": "480μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 72.977, "width_percent": 0.474}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 175}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 73.451, "width_percent": 0.296}, {"sql": "select * from `orders` where `orders`.`id` = 1021 limit 1", "type": "query", "params": [], "bindings": ["1021"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\TransactionService\\TransactionService.php", "line": 117}, {"index": 17, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 175}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Services\\TransactionService\\TransactionService.php:117", "connection": "foodyman", "start_percent": 73.748, "width_percent": 0.415}, {"sql": "select * from `users` where `users`.`id` in (111)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\TransactionService\\TransactionService.php", "line": 117}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 175}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Services\\TransactionService\\TransactionService.php:117", "connection": "foodyman", "start_percent": 74.163, "width_percent": 0.464}, {"sql": "select * from `payments` where `active` = 1 and `payments`.`id` = 4 and `payments`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\TransactionService\\TransactionService.php", "line": 292}, {"index": 17, "namespace": null, "name": "\\app\\Services\\TransactionService\\TransactionService.php", "line": 123}, {"index": 18, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 175}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Services\\TransactionService\\TransactionService.php:292", "connection": "foodyman", "start_percent": 74.627, "width_percent": 0.464}, {"sql": "select * from `transactions` where `transactions`.`payable_type` = 'App\\Models\\Order' and `transactions`.`payable_id` = 1021 and `transactions`.`payable_id` is not null and `type` = 'model' and `parent_id` is null and `transactions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Order", "1021", "model"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\TransactionService\\TransactionService.php", "line": 135}, {"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 175}, {"index": 25, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Services\\TransactionService\\TransactionService.php:135", "connection": "foodyman", "start_percent": 75.091, "width_percent": 0.405}, {"sql": "select * from `transactions` where `transactions`.`payable_type` = 'App\\Models\\Order' and `transactions`.`payable_id` = 1021 and `transactions`.`payable_id` is not null and `type` = 'model' and `parent_id` is null and `parent_id` is null and (`payable_id` = 1021 and `payable_type` = 'App\\Models\\Order' and `type` = 'model') and `transactions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Order", "1021", "model", "1021", "App\\Models\\Order", "model"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Traits\\Payable.php", "line": 22}, {"index": 21, "namespace": null, "name": "\\app\\Services\\TransactionService\\TransactionService.php", "line": 165}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 175}, {"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Traits\\Payable.php:22", "connection": "foodyman", "start_percent": 75.496, "width_percent": 0.366}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'transactions'", "type": "query", "params": [], "bindings": ["foodyman", "transactions"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Traits\\Payable.php", "line": 22}, {"index": 18, "namespace": null, "name": "\\app\\Services\\TransactionService\\TransactionService.php", "line": 165}, {"index": 19, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 175}, {"index": 23, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.0009599999999999999, "duration_str": "960μs", "stmt_id": "\\app\\Traits\\Payable.php:22", "connection": "foodyman", "start_percent": 75.862, "width_percent": 0.949}, {"sql": "insert into `transactions` (`payable_id`, `payable_type`, `type`, `price`, `user_id`, `payment_sys_id`, `payment_trx_id`, `note`, `perform_time`, `status_description`, `status`, `request`, `updated_at`, `created_at`) values (1021, 'App\\Models\\Order', 'model', 136.78, 111, 4, '', 1021, '2025-07-26 13:59:25', 'Transaction for order #1021', 'progress', '', '2025-07-26 13:59:25', '2025-07-26 13:59:25')", "type": "query", "params": [], "bindings": ["1021", "App\\Models\\Order", "model", "136.78", "111", "4", "", "1021", "2025-07-26 13:59:25", "Transaction for order #1021", "progress", "", "2025-07-26 13:59:25", "2025-07-26 13:59:25"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Traits\\Payable.php", "line": 22}, {"index": 19, "namespace": null, "name": "\\app\\Services\\TransactionService\\TransactionService.php", "line": 165}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 175}, {"index": 24, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Traits\\Payable.php:22", "connection": "foodyman", "start_percent": 76.811, "width_percent": 0.425}, {"sql": "select * from `shops` where `shops`.`id` = 501 limit 1", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 183}, {"index": 25, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.0008399999999999999, "duration_str": "840μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:183", "connection": "foodyman", "start_percent": 77.235, "width_percent": 0.83}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 153}, {"index": 9, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 10, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 721}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 128}, {"index": 16, "namespace": "middleware", "name": "sanctum.check", "line": 18}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 18, "namespace": "middleware", "name": "block.ip", "line": 24}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Middleware\\TrustLicence.php", "line": 65}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "throttle", "line": 127}, {"index": 25, "namespace": "middleware", "name": "throttle", "line": 58}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 103}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 719}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 698}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 662}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 651}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 167}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 128}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}, {"index": 37, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 40}, {"index": 41, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 42, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php", "line": 27}, {"index": 43, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 86}, {"index": 45, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 46, "namespace": null, "name": "\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php", "line": 52}, {"index": 47, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 48, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php", "line": 39}, {"index": 49, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0, "duration_str": "", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:153", "connection": "foodyman", "start_percent": 78.065, "width_percent": 0}, {"sql": "select * from `orders` where `id` = 1021 limit 1", "type": "query", "params": [], "bindings": ["1021"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 78.065, "width_percent": 0.543}, {"sql": "select `users`.*, (select count(*) from `orders` where `users`.`id` = `orders`.`user_id` and `status` = 'delivered') as `orders_count`, (select sum(`orders`.`total_price`) from `orders` where `users`.`id` = `orders`.`user_id` and `status` = 'delivered') as `orders_sum_total_price` from `users` where `users`.`id` in (111)", "type": "query", "params": [], "bindings": ["delivered", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 78.609, "width_percent": 0.702}, {"sql": "select * from `reviews` where `reviews`.`reviewable_id` in (1021) and `reviews`.`reviewable_type` = 'App\\Models\\Order' and `reviews`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Order"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 79.31, "width_percent": 0.336}, {"sql": "select * from `point_histories` where `point_histories`.`order_id` in (1021) and `point_histories`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 79.646, "width_percent": 0.622}, {"sql": "select * from `currencies` where `currencies`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 80.269, "width_percent": 0.455}, {"sql": "select `users`.*, (select avg(`reviews`.`rating`) from `reviews` where `users`.`id` = `reviews`.`assignable_id` and `reviews`.`assignable_type` = 'App\\Models\\User' and `reviews`.`deleted_at` is null) as `assign_reviews_avg_rating` from `users` where 0 = 1", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 80.723, "width_percent": 0.336}, {"sql": "select * from `order_coupons` where `order_coupons`.`order_id` in (1021)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 81.059, "width_percent": 0.287}, {"sql": "select `id`, `location`, `tax`, `price`, `price_per_km`, `background_img`, `logo_img`, `uuid`, `phone` from `shops` where `shops`.`id` in (501)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00026000000000000003, "duration_str": "260μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 81.346, "width_percent": 0.257}, {"sql": "select * from `shop_translations` where `shop_translations`.`shop_id` in (501) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `shop_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 81.603, "width_percent": 0.316}, {"sql": "select * from `order_details` where `order_details`.`order_id` in (1021) and `parent_id` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 81.919, "width_percent": 0.534}, {"sql": "select * from `stocks` where `stocks`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 82.452, "width_percent": 0.296}, {"sql": "select * from `products` where `products`.`id` in (2) and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 31, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0005600000000000001, "duration_str": "560μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 82.749, "width_percent": 0.553}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` in (2) and `start` <= '2025-07-26 00:00:00' and `end` >= '2025-07-26 00:00:00' and `active` = 1 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-07-26 00:00:00", "2025-07-26 00:00:00", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 35, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 36, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 37, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 83.302, "width_percent": 0.504}, {"sql": "select * from `units` where `units`.`id` in (2) and `units`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 36, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 37, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 83.806, "width_percent": 0.395}, {"sql": "select * from `unit_translations` where `unit_translations`.`unit_id` in (2) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `unit_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 41, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 42, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 43, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 45, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 84.201, "width_percent": 0.494}, {"sql": "select * from `product_translations` where `product_translations`.`product_id` in (2) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `product_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 36, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 37, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 84.695, "width_percent": 0.375}, {"sql": "select `extra_values`.*, `stock_extras`.`stock_id` as `pivot_stock_id`, `stock_extras`.`extra_value_id` as `pivot_extra_value_id` from `extra_values` inner join `stock_extras` on `extra_values`.`id` = `stock_extras`.`extra_value_id` where `stock_extras`.`stock_id` in (7) and `extra_values`.`deleted_at` is null order by `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 30, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 31, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 85.071, "width_percent": 0.425}, {"sql": "select * from `order_details` where `order_details`.`parent_id` in (525)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 85.496, "width_percent": 0.346}, {"sql": "select * from `order_refunds` where `order_refunds`.`order_id` in (1021)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 85.841, "width_percent": 0.484}, {"sql": "select * from `transactions` where `type` = 'model' and `parent_id` is null and `transactions`.`payable_id` in (1021) and `transactions`.`payable_type` = 'App\\Models\\Order' and `transactions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["model", "App\\Models\\Order"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 86.325, "width_percent": 0.514}, {"sql": "select * from `payments` where `payments`.`id` in (4) and `payments`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 86.839, "width_percent": 0.464}, {"sql": "select * from `galleries` where `galleries`.`loadable_id` in (1021) and `galleries`.`loadable_type` = 'App\\Models\\Order'", "type": "query", "params": [], "bindings": ["App\\Models\\Order"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 87.304, "width_percent": 0.425}, {"sql": "select * from `user_addresses` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 190}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:190", "connection": "foodyman", "start_percent": 87.728, "width_percent": 0.277}, {"sql": "select `firebase_token`, `id` from `users` where exists (select * from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `users`.`id` = `model_has_roles`.`model_id` and `model_has_roles`.`model_type` = 'App\\Models\\User' and `name` = 'admin') and `firebase_token` is not null and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", "admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Traits\\Notification.php", "line": 181}, {"index": 14, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 192}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\app\\Traits\\Notification.php:181", "connection": "foodyman", "start_percent": 88.005, "width_percent": 0.672}, {"sql": "select `firebase_token`, `id` from `users` where exists (select * from `shops` where `users`.`id` = `shops`.`user_id` and `id` = 501 and `shops`.`deleted_at` is null) and `firebase_token` is not null and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Traits\\Notification.php", "line": 189}, {"index": 14, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 192}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "\\app\\Traits\\Notification.php:189", "connection": "foodyman", "start_percent": 88.677, "width_percent": 0.751}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Traits\\Notification.php", "line": 51}, {"index": 18, "namespace": null, "name": "\\app\\Traits\\Notification.php", "line": 203}, {"index": 19, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 192}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 89.428, "width_percent": 0.583}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Traits\\Notification.php", "line": 51}, {"index": 18, "namespace": null, "name": "\\app\\Traits\\Notification.php", "line": 203}, {"index": 19, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 192}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 90.011, "width_percent": 0.316}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'push_notifications'", "type": "query", "params": [], "bindings": ["foodyman", "push_notifications"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Services\\PushNotificationService\\PushNotificationService.php", "line": 93}, {"index": 20, "namespace": null, "name": "\\app\\Traits\\Notification.php", "line": 51}, {"index": 21, "namespace": null, "name": "\\app\\Traits\\Notification.php", "line": 203}, {"index": 22, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 192}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.0008399999999999999, "duration_str": "840μs", "stmt_id": "\\app\\Services\\PushNotificationService\\PushNotificationService.php:93", "connection": "foodyman", "start_percent": 90.327, "width_percent": 0.83}, {"sql": "insert into `push_notifications` (`type`, `title`, `body`, `data`, `user_id`, `updated_at`, `created_at`) values ('', '1021', 'New order for you # 1021', '{\\\"id\\\":1021,\\\"status\\\":\\\"accepted\\\",\\\"delivery_type\\\":\\\"delivery\\\"}', 101, '2025-07-26 13:59:25', '2025-07-26 13:59:25')", "type": "query", "params": [], "bindings": ["", "1021", "New order for you # 1021", "{&quot;id&quot;:1021,&quot;status&quot;:&quot;accepted&quot;,&quot;delivery_type&quot;:&quot;delivery&quot;}", "101", "2025-07-26 13:59:25", "2025-07-26 13:59:25"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\PushNotificationService\\PushNotificationService.php", "line": 93}, {"index": 21, "namespace": null, "name": "\\app\\Traits\\Notification.php", "line": 51}, {"index": 22, "namespace": null, "name": "\\app\\Traits\\Notification.php", "line": 203}, {"index": 23, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 192}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.00147, "duration_str": "1.47ms", "stmt_id": "\\app\\Services\\PushNotificationService\\PushNotificationService.php:93", "connection": "foodyman", "start_percent": 91.157, "width_percent": 1.452}, {"sql": "insert into `push_notifications` (`type`, `title`, `body`, `data`, `user_id`, `updated_at`, `created_at`) values ('', '1021', 'New order for you # 1021', '{\\\"id\\\":1021,\\\"status\\\":\\\"accepted\\\",\\\"delivery_type\\\":\\\"delivery\\\"}', 103, '2025-07-26 13:59:25', '2025-07-26 13:59:25')", "type": "query", "params": [], "bindings": ["", "1021", "New order for you # 1021", "{&quot;id&quot;:1021,&quot;status&quot;:&quot;accepted&quot;,&quot;delivery_type&quot;:&quot;delivery&quot;}", "103", "2025-07-26 13:59:25", "2025-07-26 13:59:25"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\PushNotificationService\\PushNotificationService.php", "line": 93}, {"index": 21, "namespace": null, "name": "\\app\\Traits\\Notification.php", "line": 51}, {"index": 22, "namespace": null, "name": "\\app\\Traits\\Notification.php", "line": 203}, {"index": 23, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 192}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.00085, "duration_str": "850μs", "stmt_id": "\\app\\Services\\PushNotificationService\\PushNotificationService.php:93", "connection": "foodyman", "start_percent": 92.609, "width_percent": 0.84}, {"sql": "select `value` from `settings` where `key` = 'project_id' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["project_id"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Traits\\Notification.php", "line": 215}, {"index": 17, "namespace": null, "name": "\\app\\Traits\\Notification.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\app\\Traits\\Notification.php", "line": 203}, {"index": 19, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 192}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Traits\\Notification.php:215", "connection": "foodyman", "start_percent": 93.449, "width_percent": 0.296}, {"sql": "select * from `settings` where `key` = 'order_auto_approved' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["order_auto_approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 194}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Services\\OrderService\\OrderService.php:194", "connection": "foodyman", "start_percent": 93.746, "width_percent": 0.741}, {"sql": "select `notifications`.*, `notification_user`.`user_id` as `pivot_user_id`, `notification_user`.`notification_id` as `pivot_notification_id`, `notification_user`.`active` as `pivot_active` from `notifications` inner join `notification_user` on `notifications`.`id` = `notification_user`.`notification_id` where `notification_user`.`user_id` = 111 and `notifications`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["111"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Helpers\\NotificationHelper.php", "line": 62}, {"index": 20, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 195}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00061, "duration_str": "610μs", "stmt_id": "\\app\\Helpers\\NotificationHelper.php:62", "connection": "foodyman", "start_percent": 94.487, "width_percent": 0.603}, {"sql": "select * from `translations` where (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `key` = 'accepted' and `translations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Helpers\\NotificationHelper.php", "line": 84}, {"index": 16, "namespace": null, "name": "\\app\\Services\\OrderService\\OrderService.php", "line": 195}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\OrderController.php", "line": 104}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00267, "duration_str": "2.67ms", "stmt_id": "\\app\\Helpers\\NotificationHelper.php:84", "connection": "foodyman", "start_percent": 95.089, "width_percent": 2.638}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (111) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 22}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}, {"index": 27, "namespace": null, "name": "\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 54}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:188", "connection": "foodyman", "start_percent": 97.727, "width_percent": 0.593}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 501 and `orders`.`shop_id` is not null and (`shop_id` = 501 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["501", "501", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 98.32, "width_percent": 0.375}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 501 and `orders`.`shop_id` is not null and (`shop_id` = 501 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["501", "501", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 98.696, "width_percent": 0.336}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 111 and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["111", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\User.php", "line": 212}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Resources\\UserResource.php", "line": 46}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\app\\Models\\User.php:212", "connection": "foodyman", "start_percent": 99.032, "width_percent": 0.583}, {"sql": "select * from `orders` where `orders`.`id` = 1021 limit 1", "type": "query", "params": [], "bindings": ["1021"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\OrderDetail.php", "line": 149}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Resources\\OrderDetailResource.php", "line": 27}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 59}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Models\\OrderDetail.php:149", "connection": "foodyman", "start_percent": 99.615, "width_percent": 0.385}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": 2, "App\\Models\\Translation": 1, "App\\Models\\Transaction": 1, "App\\Models\\ProductTranslation": 1, "App\\Models\\UnitTranslation": 1, "App\\Models\\Unit": 1, "App\\Models\\ShopTranslation": 1, "App\\Models\\Payment": 2, "App\\Models\\OrderDetail": 2, "App\\Models\\Order": 4, "App\\Models\\Product": 3, "App\\Models\\Stock": 4, "App\\Models\\Shop": 7, "App\\Models\\CartDetail": 3, "App\\Models\\UserCart": 5, "App\\Models\\Cart": 4, "App\\Models\\Settings": 5, "App\\Models\\User": 6, "Laravel\\Sanctum\\PersonalAccessToken": 1, "App\\Models\\Currency": 13, "App\\Models\\Language": 10}, "count": 77}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f7bff85-f81a-4952-abfb-d65829f435c3\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/dashboard/user/orders", "status_code": "<pre class=sf-dump id=sf-dump-1549744524 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1549744524\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1413809259 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"5 characters\">pt-BR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1413809259\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-859995739 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"19 characters\">-18.***************</span>\"\n    \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"18 characters\">-50.**************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"66 characters\">Rua 3, 36 - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>&#243;polis - GO, 75860-000, Brasil</span>\"\n    \"<span class=sf-dump-key>office</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>house</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>floor</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>delivery_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-26</span>\"\n  \"<span class=sf-dump-key>delivery_time</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15:30</span>\"\n  \"<span class=sf-dump-key>delivery_type</span>\" => \"<span class=sf-dump-str title=\"8 characters\">delivery</span>\"\n  \"<span class=sf-dump-key>notes</span>\" => []\n  \"<span class=sf-dump-key>payment_category</span>\" => \"<span class=sf-dump-str title=\"15 characters\">pay_on_delivery</span>\"\n  \"<span class=sf-dump-key>currency_id</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>rate</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>shop_id</span>\" => <span class=sf-dump-num>501</span>\n  \"<span class=sf-dump-key>cart_id</span>\" => <span class=sf-dump-num>21</span>\n  \"<span class=sf-dump-key>payment_id</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>receipt_discount</span>\" => <span class=sf-dump-num>0</span>\n  \"<span class=sf-dump-key>receipt_count</span>\" => <span class=sf-dump-num>0</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-859995739\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1541502771 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">401</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">Bearer 30|tvXEmOWgkCv9xzLPD6ajFosDUNhRtNEj9x4OKwkr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1541502771\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-258523121 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">64279</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.26 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"40 characters\">/api/v1/dashboard/user/orders?lang=pt-BR</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"52 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/api/v1/dashboard/user/orders</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"39 characters\">/index.php/api/v1/dashboard/user/orders</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"10 characters\">lang=pt-BR</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">401</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">401</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_AUTHORIZATION</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Bearer 30|tvXEmOWgkCv9xzLPD6ajFosDUNhRtNEj9x4OKwkr</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3001</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3001/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753549164.7148</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753549164</span>\n  \"<span class=sf-dump-key>argv</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">lang=pt-BR</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>argc</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-258523121\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2051113360 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2051113360\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1341878495 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 26 Jul 2025 16:59:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5000</span>\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>4972</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1341878495\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1130135966 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1130135966\", {\"maxDepth\":0})</script>\n"}}