{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\seller-views\\\\users\\\\components\\\\form\\\\add\\\\add.js\",\n  _s = $RefreshSig$();\nimport { Button, Card, Form } from 'antd';\nimport { useTranslation } from 'react-i18next';\nimport { useState } from 'react';\nimport moment from 'configs/moment-config';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport userService from 'services/seller/user';\nimport { removeFromMenu } from 'redux/slices/menu';\nimport { toast } from 'react-toastify';\nimport InputFields from './input-fields';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ShopUsersAdd = () => {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    t\n  } = useTranslation();\n  const params = useParams();\n  const {\n    activeMenu\n  } = useSelector(state => state.menu, shallowEqual);\n  const [form] = Form.useForm();\n  const [avatar, setAvatar] = useState([]);\n  const [loadingBtn, setLoadingBtn] = useState(false);\n  const onFinish = values => {\n    var _values$gender;\n    const body = {\n      images: avatar !== null && avatar !== void 0 && avatar.length ? avatar === null || avatar === void 0 ? void 0 : avatar.map(item => item === null || item === void 0 ? void 0 : item.name) : undefined,\n      firstname: values === null || values === void 0 ? void 0 : values.firstname,\n      lastname: values === null || values === void 0 ? void 0 : values.lastname,\n      birthday: moment(values === null || values === void 0 ? void 0 : values.birthday).format('YYYY-MM-DD'),\n      gender: values === null || values === void 0 ? void 0 : (_values$gender = values.gender) === null || _values$gender === void 0 ? void 0 : _values$gender.value,\n      phone: values === null || values === void 0 ? void 0 : values.phone,\n      email: values === null || values === void 0 ? void 0 : values.email,\n      password: values === null || values === void 0 ? void 0 : values.password,\n      password_confirmation: values === null || values === void 0 ? void 0 : values.password_confirmation,\n      role: params === null || params === void 0 ? void 0 : params.role\n    };\n    setLoadingBtn(true);\n    userService.create(body).then(() => {\n      toast.success(t('user.successfully.added'));\n      const nextUrl = 'seller/shop-users';\n      dispatch(removeFromMenu({\n        ...activeMenu,\n        nextUrl\n      }));\n      navigate(`/${nextUrl}?role=${params === null || params === void 0 ? void 0 : params.role}`);\n    }).finally(() => {\n      setLoadingBtn(false);\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Form, {\n    form: form,\n    layout: \"vertical\",\n    onFinish: onFinish,\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(InputFields, {\n        avatar: avatar,\n        setAvatar: setAvatar,\n        form: form\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        htmlType: \"submit\",\n        loading: loadingBtn,\n        children: t('save')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(ShopUsersAdd, \"1HbfmoWGx4Eq2rHCQdkL2s0Gxbk=\", false, function () {\n  return [useNavigate, useDispatch, useTranslation, useParams, useSelector, Form.useForm];\n});\n_c = ShopUsersAdd;\nexport default ShopUsersAdd;\nvar _c;\n$RefreshReg$(_c, \"ShopUsersAdd\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Card", "Form", "useTranslation", "useState", "moment", "shallowEqual", "useDispatch", "useSelector", "useNavigate", "useParams", "userService", "removeFromMenu", "toast", "InputFields", "jsxDEV", "_jsxDEV", "ShopUsersAdd", "_s", "navigate", "dispatch", "t", "params", "activeMenu", "state", "menu", "form", "useForm", "avatar", "set<PERSON>vat<PERSON>", "loadingBtn", "setLoadingBtn", "onFinish", "values", "_values$gender", "body", "images", "length", "map", "item", "name", "undefined", "firstname", "lastname", "birthday", "format", "gender", "value", "phone", "email", "password", "password_confirmation", "role", "create", "then", "success", "nextUrl", "finally", "layout", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "htmlType", "loading", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/seller-views/users/components/form/add/add.js"], "sourcesContent": ["import { But<PERSON>, Card, Form } from 'antd';\nimport { useTranslation } from 'react-i18next';\nimport { useState } from 'react';\nimport moment from 'configs/moment-config';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport userService from 'services/seller/user';\nimport { removeFromMenu } from 'redux/slices/menu';\nimport { toast } from 'react-toastify';\nimport InputFields from './input-fields';\n\nconst ShopUsersAdd = () => {\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const { t } = useTranslation();\n  const params = useParams();\n  const { activeMenu } = useSelector((state) => state.menu, shallowEqual);\n  const [form] = Form.useForm();\n  const [avatar, setAvatar] = useState([]);\n  const [loadingBtn, setLoadingBtn] = useState(false);\n  const onFinish = (values) => {\n    const body = {\n      images: avatar?.length ? avatar?.map((item) => item?.name) : undefined,\n      firstname: values?.firstname,\n      lastname: values?.lastname,\n      birthday: moment(values?.birthday).format('YYYY-MM-DD'),\n      gender: values?.gender?.value,\n      phone: values?.phone,\n      email: values?.email,\n      password: values?.password,\n      password_confirmation: values?.password_confirmation,\n      role: params?.role,\n    };\n    setLoadingBtn(true);\n    userService\n      .create(body)\n      .then(() => {\n        toast.success(t('user.successfully.added'));\n        const nextUrl = 'seller/shop-users';\n        dispatch(removeFromMenu({ ...activeMenu, nextUrl }));\n        navigate(`/${nextUrl}?role=${params?.role}`);\n      })\n      .finally(() => {\n        setLoadingBtn(false);\n      });\n  };\n  return (\n    <Form form={form} layout='vertical' onFinish={onFinish}>\n      <Card>\n        <InputFields avatar={avatar} setAvatar={setAvatar} form={form} />\n      </Card>\n      <Card>\n        <Button type='primary' htmlType='submit' loading={loadingBtn}>\n          {t('save')}\n        </Button>\n      </Card>\n    </Form>\n  );\n};\n\nexport default ShopUsersAdd;\n"], "mappings": ";;AAAA,SAASA,MAAM,EAAEC,IAAI,EAAEC,IAAI,QAAQ,MAAM;AACzC,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,WAAW,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc;EAAE,CAAC,GAAGlB,cAAc,CAAC,CAAC;EAC9B,MAAMmB,MAAM,GAAGZ,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAEa;EAAW,CAAC,GAAGf,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAEnB,YAAY,CAAC;EACvE,MAAM,CAACoB,IAAI,CAAC,GAAGxB,IAAI,CAACyB,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM4B,QAAQ,GAAIC,MAAM,IAAK;IAAA,IAAAC,cAAA;IAC3B,MAAMC,IAAI,GAAG;MACXC,MAAM,EAAER,MAAM,aAANA,MAAM,eAANA,MAAM,CAAES,MAAM,GAAGT,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEU,GAAG,CAAEC,IAAI,IAAKA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,IAAI,CAAC,GAAGC,SAAS;MACtEC,SAAS,EAAET,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAES,SAAS;MAC5BC,QAAQ,EAAEV,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEU,QAAQ;MAC1BC,QAAQ,EAAEvC,MAAM,CAAC4B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEW,QAAQ,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC;MACvDC,MAAM,EAAEb,MAAM,aAANA,MAAM,wBAAAC,cAAA,GAAND,MAAM,CAAEa,MAAM,cAAAZ,cAAA,uBAAdA,cAAA,CAAgBa,KAAK;MAC7BC,KAAK,EAAEf,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEe,KAAK;MACpBC,KAAK,EAAEhB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEgB,KAAK;MACpBC,QAAQ,EAAEjB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEiB,QAAQ;MAC1BC,qBAAqB,EAAElB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEkB,qBAAqB;MACpDC,IAAI,EAAE9B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE8B;IAChB,CAAC;IACDrB,aAAa,CAAC,IAAI,CAAC;IACnBpB,WAAW,CACR0C,MAAM,CAAClB,IAAI,CAAC,CACZmB,IAAI,CAAC,MAAM;MACVzC,KAAK,CAAC0C,OAAO,CAAClC,CAAC,CAAC,yBAAyB,CAAC,CAAC;MAC3C,MAAMmC,OAAO,GAAG,mBAAmB;MACnCpC,QAAQ,CAACR,cAAc,CAAC;QAAE,GAAGW,UAAU;QAAEiC;MAAQ,CAAC,CAAC,CAAC;MACpDrC,QAAQ,CAAE,IAAGqC,OAAQ,SAAQlC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE8B,IAAK,EAAC,CAAC;IAC9C,CAAC,CAAC,CACDK,OAAO,CAAC,MAAM;MACb1B,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,CAAC;EACN,CAAC;EACD,oBACEf,OAAA,CAACd,IAAI;IAACwB,IAAI,EAAEA,IAAK;IAACgC,MAAM,EAAC,UAAU;IAAC1B,QAAQ,EAAEA,QAAS;IAAA2B,QAAA,gBACrD3C,OAAA,CAACf,IAAI;MAAA0D,QAAA,eACH3C,OAAA,CAACF,WAAW;QAACc,MAAM,EAAEA,MAAO;QAACC,SAAS,EAAEA,SAAU;QAACH,IAAI,EAAEA;MAAK;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC,eACP/C,OAAA,CAACf,IAAI;MAAA0D,QAAA,eACH3C,OAAA,CAAChB,MAAM;QAACgE,IAAI,EAAC,SAAS;QAACC,QAAQ,EAAC,QAAQ;QAACC,OAAO,EAAEpC,UAAW;QAAA6B,QAAA,EAC1DtC,CAAC,CAAC,MAAM;MAAC;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAAC7C,EAAA,CA/CID,YAAY;EAAA,QACCR,WAAW,EACXF,WAAW,EACdJ,cAAc,EACbO,SAAS,EACDF,WAAW,EACnBN,IAAI,CAACyB,OAAO;AAAA;AAAAwC,EAAA,GANvBlD,YAAY;AAiDlB,eAAeA,YAAY;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}