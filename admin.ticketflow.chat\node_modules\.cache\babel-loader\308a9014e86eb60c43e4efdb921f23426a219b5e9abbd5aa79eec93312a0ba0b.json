{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\seller-views\\\\users\\\\components\\\\form\\\\add\\\\add.js\",\n  _s = $RefreshSig$();\nimport { Button, Card, Form } from 'antd';\nimport { useTranslation } from 'react-i18next';\nimport { useState } from 'react';\nimport moment from 'configs/moment-config';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport userService from 'services/seller/user';\nimport { removeFromMenu } from 'redux/slices/menu';\nimport { toast } from 'react-toastify';\nimport InputFields from './input-fields';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ShopUsersAdd = () => {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    t\n  } = useTranslation();\n  const params = useParams();\n  const {\n    activeMenu\n  } = useSelector(state => state.menu, shallowEqual);\n  const [form] = Form.useForm();\n  const [avatar, setAvatar] = useState([]);\n  const [loadingBtn, setLoadingBtn] = useState(false);\n  const onFinish = values => {\n    var _values$gender;\n    // Validate and format birthday\n    let formattedBirthday = null;\n    if (values !== null && values !== void 0 && values.birthday) {\n      const birthdayMoment = moment(values.birthday);\n      if (birthdayMoment.isValid()) {\n        formattedBirthday = birthdayMoment.format('YYYY-MM-DD');\n      } else {\n        toast.error(t('invalid.date.format'));\n        return;\n      }\n    }\n    const body = {\n      images: avatar !== null && avatar !== void 0 && avatar.length ? avatar === null || avatar === void 0 ? void 0 : avatar.map(item => item === null || item === void 0 ? void 0 : item.name) : undefined,\n      firstname: values === null || values === void 0 ? void 0 : values.firstname,\n      lastname: values === null || values === void 0 ? void 0 : values.lastname,\n      birthday: formattedBirthday,\n      gender: values === null || values === void 0 ? void 0 : (_values$gender = values.gender) === null || _values$gender === void 0 ? void 0 : _values$gender.value,\n      phone: values === null || values === void 0 ? void 0 : values.phone,\n      email: values === null || values === void 0 ? void 0 : values.email,\n      password: values === null || values === void 0 ? void 0 : values.password,\n      password_confirmation: values === null || values === void 0 ? void 0 : values.password_confirmation,\n      role: params === null || params === void 0 ? void 0 : params.role\n    };\n    setLoadingBtn(true);\n    userService.create(body).then(() => {\n      toast.success(t('user.successfully.added'));\n      const nextUrl = 'seller/shop-users';\n      dispatch(removeFromMenu({\n        ...activeMenu,\n        nextUrl\n      }));\n      navigate(`/${nextUrl}?role=${params === null || params === void 0 ? void 0 : params.role}`);\n    }).finally(() => {\n      setLoadingBtn(false);\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Form, {\n    form: form,\n    layout: \"vertical\",\n    onFinish: onFinish,\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(InputFields, {\n        avatar: avatar,\n        setAvatar: setAvatar,\n        form: form\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        htmlType: \"submit\",\n        loading: loadingBtn,\n        children: t('save')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this);\n};\n_s(ShopUsersAdd, \"1HbfmoWGx4Eq2rHCQdkL2s0Gxbk=\", false, function () {\n  return [useNavigate, useDispatch, useTranslation, useParams, useSelector, Form.useForm];\n});\n_c = ShopUsersAdd;\nexport default ShopUsersAdd;\nvar _c;\n$RefreshReg$(_c, \"ShopUsersAdd\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Card", "Form", "useTranslation", "useState", "moment", "shallowEqual", "useDispatch", "useSelector", "useNavigate", "useParams", "userService", "removeFromMenu", "toast", "InputFields", "jsxDEV", "_jsxDEV", "ShopUsersAdd", "_s", "navigate", "dispatch", "t", "params", "activeMenu", "state", "menu", "form", "useForm", "avatar", "set<PERSON>vat<PERSON>", "loadingBtn", "setLoadingBtn", "onFinish", "values", "_values$gender", "formattedBirthday", "birthday", "birthdayMoment", "<PERSON><PERSON><PERSON><PERSON>", "format", "error", "body", "images", "length", "map", "item", "name", "undefined", "firstname", "lastname", "gender", "value", "phone", "email", "password", "password_confirmation", "role", "create", "then", "success", "nextUrl", "finally", "layout", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "htmlType", "loading", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/seller-views/users/components/form/add/add.js"], "sourcesContent": ["import { Button, Card, Form } from 'antd';\nimport { useTranslation } from 'react-i18next';\nimport { useState } from 'react';\nimport moment from 'configs/moment-config';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport userService from 'services/seller/user';\nimport { removeFromMenu } from 'redux/slices/menu';\nimport { toast } from 'react-toastify';\nimport InputFields from './input-fields';\n\nconst ShopUsersAdd = () => {\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const { t } = useTranslation();\n  const params = useParams();\n  const { activeMenu } = useSelector((state) => state.menu, shallowEqual);\n  const [form] = Form.useForm();\n  const [avatar, setAvatar] = useState([]);\n  const [loadingBtn, setLoadingBtn] = useState(false);\n  const onFinish = (values) => {\n    // Validate and format birthday\n    let formattedBirthday = null;\n    if (values?.birthday) {\n      const birthdayMoment = moment(values.birthday);\n      if (birthdayMoment.isValid()) {\n        formattedBirthday = birthdayMoment.format('YYYY-MM-DD');\n      } else {\n        toast.error(t('invalid.date.format'));\n        return;\n      }\n    }\n\n    const body = {\n      images: avatar?.length ? avatar?.map((item) => item?.name) : undefined,\n      firstname: values?.firstname,\n      lastname: values?.lastname,\n      birthday: formattedBirthday,\n      gender: values?.gender?.value,\n      phone: values?.phone,\n      email: values?.email,\n      password: values?.password,\n      password_confirmation: values?.password_confirmation,\n      role: params?.role,\n    };\n    setLoadingBtn(true);\n    userService\n      .create(body)\n      .then(() => {\n        toast.success(t('user.successfully.added'));\n        const nextUrl = 'seller/shop-users';\n        dispatch(removeFromMenu({ ...activeMenu, nextUrl }));\n        navigate(`/${nextUrl}?role=${params?.role}`);\n      })\n      .finally(() => {\n        setLoadingBtn(false);\n      });\n  };\n  return (\n    <Form form={form} layout='vertical' onFinish={onFinish}>\n      <Card>\n        <InputFields avatar={avatar} setAvatar={setAvatar} form={form} />\n      </Card>\n      <Card>\n        <Button type='primary' htmlType='submit' loading={loadingBtn}>\n          {t('save')}\n        </Button>\n      </Card>\n    </Form>\n  );\n};\n\nexport default ShopUsersAdd;\n"], "mappings": ";;AAAA,SAASA,MAAM,EAAEC,IAAI,EAAEC,IAAI,QAAQ,MAAM;AACzC,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACpE,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,WAAW,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc;EAAE,CAAC,GAAGlB,cAAc,CAAC,CAAC;EAC9B,MAAMmB,MAAM,GAAGZ,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAEa;EAAW,CAAC,GAAGf,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACC,IAAI,EAAEnB,YAAY,CAAC;EACvE,MAAM,CAACoB,IAAI,CAAC,GAAGxB,IAAI,CAACyB,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM4B,QAAQ,GAAIC,MAAM,IAAK;IAAA,IAAAC,cAAA;IAC3B;IACA,IAAIC,iBAAiB,GAAG,IAAI;IAC5B,IAAIF,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEG,QAAQ,EAAE;MACpB,MAAMC,cAAc,GAAGhC,MAAM,CAAC4B,MAAM,CAACG,QAAQ,CAAC;MAC9C,IAAIC,cAAc,CAACC,OAAO,CAAC,CAAC,EAAE;QAC5BH,iBAAiB,GAAGE,cAAc,CAACE,MAAM,CAAC,YAAY,CAAC;MACzD,CAAC,MAAM;QACL1B,KAAK,CAAC2B,KAAK,CAACnB,CAAC,CAAC,qBAAqB,CAAC,CAAC;QACrC;MACF;IACF;IAEA,MAAMoB,IAAI,GAAG;MACXC,MAAM,EAAEd,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEe,MAAM,GAAGf,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEgB,GAAG,CAAEC,IAAI,IAAKA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,IAAI,CAAC,GAAGC,SAAS;MACtEC,SAAS,EAAEf,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEe,SAAS;MAC5BC,QAAQ,EAAEhB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEgB,QAAQ;MAC1Bb,QAAQ,EAAED,iBAAiB;MAC3Be,MAAM,EAAEjB,MAAM,aAANA,MAAM,wBAAAC,cAAA,GAAND,MAAM,CAAEiB,MAAM,cAAAhB,cAAA,uBAAdA,cAAA,CAAgBiB,KAAK;MAC7BC,KAAK,EAAEnB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEmB,KAAK;MACpBC,KAAK,EAAEpB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEoB,KAAK;MACpBC,QAAQ,EAAErB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEqB,QAAQ;MAC1BC,qBAAqB,EAAEtB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEsB,qBAAqB;MACpDC,IAAI,EAAElC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEkC;IAChB,CAAC;IACDzB,aAAa,CAAC,IAAI,CAAC;IACnBpB,WAAW,CACR8C,MAAM,CAAChB,IAAI,CAAC,CACZiB,IAAI,CAAC,MAAM;MACV7C,KAAK,CAAC8C,OAAO,CAACtC,CAAC,CAAC,yBAAyB,CAAC,CAAC;MAC3C,MAAMuC,OAAO,GAAG,mBAAmB;MACnCxC,QAAQ,CAACR,cAAc,CAAC;QAAE,GAAGW,UAAU;QAAEqC;MAAQ,CAAC,CAAC,CAAC;MACpDzC,QAAQ,CAAE,IAAGyC,OAAQ,SAAQtC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEkC,IAAK,EAAC,CAAC;IAC9C,CAAC,CAAC,CACDK,OAAO,CAAC,MAAM;MACb9B,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,CAAC;EACN,CAAC;EACD,oBACEf,OAAA,CAACd,IAAI;IAACwB,IAAI,EAAEA,IAAK;IAACoC,MAAM,EAAC,UAAU;IAAC9B,QAAQ,EAAEA,QAAS;IAAA+B,QAAA,gBACrD/C,OAAA,CAACf,IAAI;MAAA8D,QAAA,eACH/C,OAAA,CAACF,WAAW;QAACc,MAAM,EAAEA,MAAO;QAACC,SAAS,EAAEA,SAAU;QAACH,IAAI,EAAEA;MAAK;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC,eACPnD,OAAA,CAACf,IAAI;MAAA8D,QAAA,eACH/C,OAAA,CAAChB,MAAM;QAACoE,IAAI,EAAC,SAAS;QAACC,QAAQ,EAAC,QAAQ;QAACC,OAAO,EAAExC,UAAW;QAAAiC,QAAA,EAC1D1C,CAAC,CAAC,MAAM;MAAC;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAACjD,EAAA,CA3DID,YAAY;EAAA,QACCR,WAAW,EACXF,WAAW,EACdJ,cAAc,EACbO,SAAS,EACDF,WAAW,EACnBN,IAAI,CAACyB,OAAO;AAAA;AAAA4C,EAAA,GANvBtD,YAAY;AA6DlB,eAAeA,YAAY;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}